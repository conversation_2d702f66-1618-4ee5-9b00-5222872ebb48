import axios from 'axios'
import type { AxiosError, AxiosInstance } from 'axios'
import { computed } from 'vue'
import type { ApiResponse, ChatRequest, ChatResponse } from '../typings/chat.dify'
import { useChatStore } from './../store/chat'
import { useUserStore } from '@/store'
import { getFileType } from '@/utils/functions/index'
import cache from '@/plugins/cache'
const userStore = useUserStore()
const chatStore = useChatStore()
const appParams = computed(() => chatStore.appParams)
// 类型定义
interface TextToAudioRequest {
  message_id?: string
  text?: string
  user: string
}

interface TextToAudioResponse {
  audio: Blob
}

interface StreamEventHandlers {
  onMessage?: (text: string) => void
  onThought?: (thought: any) => void
  onFile?: (file: any) => void
  onComplete?: () => void
  onError?: (error: string) => void
}

// 配置常量
const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_DIFY_API_BASE_URL || '/api',
  TOKEN: import.meta.env.VITE_DIFY_API_TOKEN || 'app-DVzi1K7It1YoZgtOInbI1wM8',
  HEADERS: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${import.meta.env.VITE_DIFY_API_TOKEN}`,
  },
} as const

// 错误处理工具
class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public data?: any,
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// API客户端类
class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      headers: API_CONFIG.HEADERS,
    })

    // 添加响应拦截器
    this.client.interceptors.response.use(
      response => response,
      this.handleError,
    )
  }

  private handleError(error: AxiosError): never {
    if (error.response) {
      const responseData = error.response.data as { message?: string; code?: string }
      throw new ApiError(
        responseData.message || '请求失败',
        error.response.status,
        responseData.code,
        error.response.data,
      )
    }
    throw new ApiError(error.message || '网络错误')
  }

  // 发送常规消息
  async sendMessage(params: ChatRequest): Promise<ApiResponse<ChatResponse>> {
    try {
      const response = await this.client.post('/chat-messages', {
        inputs: {},
        query: params.query,
        user: params.user_id,
        conversation_id: params.conversation_id,
        response_mode: 'blocking',
      })

      return {
        success: true,
        data: {
          message_id: response.data.id,
          conversation_id: response.data.conversation_id,
          content: response.data.answer,
        },
      }
    }
    catch (error) {
      console.error('发送消息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  // 发送流式消息
  sendStreamMessage(query: string, uploadFile: any, handlers: StreamEventHandlers): void {
    const { onMessage, onThought, onFile, onComplete, onError } = handlers
    const file: { upload_file_id: any; type: string; transfer_method: string }[] = []
    // 7月28日  停在这里，需要根据文件的后缀名 反推文件的所属类型，  document/image/video等
    if (uploadFile?.length) {
      uploadFile.forEach((item: any) => {
        file.push({
          upload_file_id: item.upload_file_id || item.id,
          type: item.extension ? getFileType(item.extension) : item.type,
          transfer_method: 'local_file',
        })
      })
    }
    const params = {
      inputs: {},
      query,
      user: (() => {
        return userStore.userInfo?.userId || `user-${Date.now()}`
      })(),
      response_mode: 'streaming',
      conversation_id: (() => {
        return chatStore.currentSession?.id || ''
      })(),
      files: uploadFile?.length ? file : [],
    }

    // 使用完整的API URL
    const apiUrl = `${API_CONFIG.BASE_URL}/chat-messages`

    console.log('发送流式请求:', apiUrl, params)

    fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_CONFIG.TOKEN}`,
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify(params),
    })
      .then(this.handleStreamResponse(onMessage, onThought, onFile, onComplete, onError))
      .catch((error) => {
        console.error('流式请求失败:', error)
        onError?.(error.message)
      })
  }

  private handleStreamResponse(
    onMessage?: (text: string) => void,
    onThought?: (thought: any) => void,
    onFile?: (file: any) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
  ) {
    return async (response: Response) => {
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`)

      if (!response.body)
        throw new Error('Response body is null')

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      const processChunk = async () => {
        try {
          const { done, value } = await reader.read()

          if (done) {
            console.log('流式响应完成',done,value)
            onComplete?.()
            return
          }

          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim().startsWith('data: '))
              await this.processStreamLine(line, onMessage, onThought, onFile, onError)
          }

          await processChunk()
        }
        catch (error) {
          console.error('处理流数据失败:', error)
          onError?.(error instanceof Error ? error.message : '处理流数据失败')
        }
      }

      await processChunk()
    }
  }

  private async processStreamLine(
    line: string,
    onMessage?: (text: string, id: string) => void,
    onThought?: (thought: any) => void,
    onFile?: (file: any) => void,
    onError?: (error: string) => void,
  ) {
    try {
      const jsonStr = line.substring(5).trim()
      if (!jsonStr)
        return

      const eventData = JSON.parse(jsonStr)
      if (cache.session.get('taskId') !== eventData.task_id || cache.session.get('taskId') == null)
        cache.session.set('taskId', eventData.task_id)

      switch (eventData.event) {
        case 'message':
        case 'agent_message':
          if (eventData.answer !== undefined)
            onMessage?.(eventData.answer, eventData.message_id)

          break

        case 'agent_thought':
          onThought?.(eventData)
          break

        case 'message_file':
          onFile?.(eventData)
          break

        case 'message_end':
        case 'tts_message_end':
          console.log('接收到消息结束事件')
          break

        case 'error':
          console.error('流中的错误:', eventData)
          onError?.(eventData.error || eventData.message || '流处理错误')
          break

        default:
          console.log('未处理的事件类型:', eventData.event)
      }
    }
    catch (error) {
      console.error('处理流数据行失败:', error)
      onError?.(error instanceof Error ? error.message : '处理流数据行失败')
    }
  }

  // 文字转语音
  async textToAudio(params: TextToAudioRequest): Promise<ApiResponse<TextToAudioResponse>> {
    try {
      const requestBody = {
        text: params.text,
        message_id: params.message_id,
        user: params.user,
      }

      console.log('发送文字转语音请求:', {
        url: `${API_CONFIG.BASE_URL}/text-to-audio`,
        body: requestBody,
      })

      const response = await axios.post(`${API_CONFIG.BASE_URL}/text-to-audio`, requestBody, {
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${API_CONFIG.TOKEN}`,
          'Content-Type': 'application/json',
        },
      })

      return {
        success: true,
        data: {
          audio: response.data,
        },
      }
    }
    catch (error) {
      console.error('文字转语音失败:', error)
      if (axios.isAxiosError(error) && error.response) {
        console.error('错误详情:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
        })
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  // 用于获取应用的基本信息
  async getAppInfo(): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.get('/info')
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('获取应用信息失败:', error)
    }
  }

  // 用于获取应用的参数
  async getAppParams(): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.get('/parameters')
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('获取应用参数失败:', error)
    }
  }

  // 用于获取工具icon
  async getAppMeta(): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.get('/meta')
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('获取应用meta信息失败:', error)
    }
  }

  // 用于获取会话列表
  async getSessionList(param: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.get('/conversations', { params: param })
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('获取获取会话列表失败:', error)
    }
  }

  // 用于删除会话
  async delSession(conversation_id: any, data: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.delete(`/conversations/${conversation_id}`, { data })
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('删除会话失败:', error)
    }
  }

  // 会话重命名
  async renameSession(conversation_id: string, data: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.post(`/conversations/${conversation_id}/name`, data)
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('会话重命名失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  // 用于获取会话消息
  async getSessionMessages(param: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.get('/messages', { params: param })
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('获取获取会话消息失败:', error)
    }
  }

  // 用于消息点赞点踩反馈
  async feedbacksMes(message_id: string, data: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.post(`/messages/${message_id}/feedbacks`, data)
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('消息反馈操作失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  // 用于消息停止响应
  async stopTask(task_id: string | null, data: any): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.post(`/chat-messages/${task_id}/stop`, data)
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  // 上传文件
  async uploadFile(data: FormData): Promise<ApiResponse<any> | undefined> {
    try {
      const response = await this.client.post('/files/upload', data)
      return {
        success: true,
        data: response.data,
      }
    }
    catch (error) {
      console.error('上传文件失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }
}

// 创建API客户端实例
const apiClient = new ApiClient()

// 导出API函数
export const chatApi = {
  sendMessage: apiClient.sendMessage.bind(apiClient),
  sendStreamMessage: apiClient.sendStreamMessage.bind(apiClient),
  textToAudio: apiClient.textToAudio.bind(apiClient),
  getAppInfo: apiClient.getAppInfo.bind(apiClient),
  getAppParams: apiClient.getAppParams.bind(apiClient),
  getAppMeta: apiClient.getAppMeta.bind(apiClient),
  getSessionList: apiClient.getSessionList.bind(apiClient),
  delSession: apiClient.delSession.bind(apiClient),
  renameSession: apiClient.renameSession.bind(apiClient),
  getSessionMessages: apiClient.getSessionMessages.bind(apiClient),
  uploadFile: apiClient.uploadFile.bind(apiClient),
  feedbacksMes: apiClient.feedbacksMes.bind(apiClient),
  stopTask: apiClient.stopTask.bind(apiClient),
}

// 为了向后兼容，也导出单独的函数
export const { sendMessage, sendStreamMessage, textToAudio, getAppInfo, getAppParams, getSessionList, delSession, renameSession, getSessionMessages, feedbacksMes, getAppMeta, uploadFile, stopTask } = chatApi
