<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import {
  NButton,
  NCard,
  NDivider,
  NIcon,
  NPagination,
  NTabPane,
  NTabs,
  useMessage,
} from 'naive-ui'
import to from 'await-to-js'
import { useRouter } from 'vue-router'
import image1 from './../../assets/cp-icon-1.png'
import image2 from './../../assets/cp-icon-2.png'
import image3 from './../../assets/cp-icon-3.png'
import image4 from './../../assets/cp-icon-4.png'
import image5 from './../../assets/cp-icon-5.png'
import image6 from './../../assets/cp-icon-6.png'
import image7 from './../../assets/cp-icon-7.png'
import image8 from './../../assets/cp-icon-8.png'
import image9 from './../../assets/cp-icon-9.png'
import type { Character } from '@/api/store'
import { copyRoleList, getAppList } from '@/api/store'
import { t } from '@/locales'
import { useAppStore, useChatStore } from '@/store'
const allData = ref<Character[]>([])
const currentPage = ref(1)
const pageSize = ref(9)
const message = useMessage()
const router = useRouter()
const handList = [
  {
    appUrl: 'https://k.fitkc.cn/chat/t53nctLjuOiKDtrX',
    avatar: image4,
    description: 'DeepSeek项目立项书写手',
    id: 99,
    name: '项目立项书智能写手',
    remark: null,
    btnText: '开始立项',
    type: 'lx',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/NWL7LnV2gGy65pxw',
    avatar: image5,
    description: '企业政策达人',
    id: 98,
    name: '政策智脑',
    remark: null,
    btnText: '快速测评',
    type: 'zc',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/fJokI6IWsauydItf',
    avatar: image6,
    description: '智能测评小助手',
    id: 97,
    name: '高企智能评测',
    remark: null,
    btnText: '快速测评',
    type: 'cp',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/0JVStUaU5axyO2x4',
    avatar: image1,
    description: '智能测评小助手',
    id: 96,
    name: '创新型中小企业',
    remark: null,
    btnText: '快速测评',
    type: 'cp',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/FcNFJRlFTM3TJn5Q',
    avatar: image3,
    description: '智能测评小助手',
    id: 95,
    name: '专精特新中小企业',
    remark: null,
    btnText: '快速测评',
    type: 'cp',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/Or0EEfQhFR37olsW',
    avatar: image2,
    description: '智能测评小助手',
    id: 94,
    name: '专精特新小巨人',
    remark: null,
    btnText: '快速测评',
    type: 'cp',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/c5oUfSOakFnS9brE',
    avatar: image9,
    description: '专注为科技型企业提供咨询服务',
    id: 93,
    name: '飞地智脑',
    remark: null,
    btnText: '开始提问',
    type: 'zx',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/c5oUfSOakFnS9brE',
    avatar: image7,
    description: '科创咨询服务专家',
    id: 92,
    name: '高企申报智能顾问',
    remark: null,
    btnText: '开始咨询',
    type: 'zx',
  },
  {
    appUrl: 'https://k.fitkc.cn/chat/6oRLP8gFgPCgvyK0',
    avatar: image8,
    description: '技术转移领域专家',
    id: 91,
    name: '产学研智能对接',
    remark: null,
    btnText: '开始咨询',
    type: 'zx',
  },
]

onMounted(async () => {
  const [err, result] = await to(getAppList())
  if (err)
    message.error(err.message)
  else allData.value = [...handList, ...result.data]
})

const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return allData.value.slice(start, start + pageSize.value)
})

const totalItems = computed(() => {
  return allData.value ? allData.value.length : 0
})

const appStore = useAppStore()
const chatStore = useChatStore()

function playApp(url: string) {
  appStore.setIsChat(false)
  // 跳转到应用
  if (url.startsWith('http'))
    router.push(`/store/agent?href=${encodeURIComponent(url)}`)
  else router.push(url)
}
function getClass(flag: boolean) {
  if (flag)
    return 'card-item'

  return 'hidden'
}

async function handleActionButtonClick(item: Character) {
  const [err] = await to(copyRoleList(item))
  if (err)
    message.error(err.message)
  else message.success(t('voice.collectionSuccessful'))
}
</script>

<template>
  <div class="flex h-full flex-col role-card">
    <NTabs type="line" class="tab-bar">
      <NTabPane name="officialRecommend" tab="官网推荐">
        <main class="flex-1 overflow-hidden" style="margin-left: 20px">
          <div class="card-container">
            <NCard
              v-for="item in tableData"
              :key="item.id"
              class="card-item"
              bordered
              hoverable
            >
              <div class="flex justify-between">
                <div>
                  <h3>{{ item.name }}</h3>
                  <p class="ellipsis" :title="item.description">
                    {{ item.description || "——" }}
                  </p>
                </div>
                <!-- <n-avatar :size="48" :src="item.avatar" /> -->

                <NIcon size="48">
                  <img :src="item.avatar" alt="Icon" style="height: 48px">
                </NIcon>
              </div>
              <NDivider />
              <div class="flex justify-between mt-4 button-list">
                <NButton secondary round type="info" @click="playApp(item.appUrl)">
                  {{ item.btnText || "立即体验" }}
                </NButton>
                <!-- <n-button  secondary round  type="primary">
				          关注
				          {{ $t('voice.collection') }}
				        </n-button> -->
              </div>
            </NCard>
          </div>
          <NPagination
            :page="currentPage"
            :page-size="pageSize"
            :item-count="totalItems"
            class="pagination voice-pagination"
            @update:page="currentPage = $event"
          />
        </main>
      </NTabPane>
      <NTabPane name="writingAssist" tab="政策资金·智能匹配">
        <main class="flex-1 overflow-hidden" style="margin-left: 20px">
          <div class="card-container">
            <NCard
              v-for="item in tableData"
              :key="item.id"
              :class="getClass(item.type === 'zc')"
              bordered
              hoverable
            >
              <div class="flex justify-between">
                <div>
                  <h3>{{ item.name }}</h3>
                  <p class="ellipsis" :title="item.description">
                    {{ item.description || "——" }}
                  </p>
                </div>
                <!-- <n-avatar :size="48" :src="item.avatar" /> -->
                <NIcon size="48">
                  <img :src="item.avatar" alt="Icon">
                </NIcon>
              </div>
              <NDivider />
              <div class="flex justify-between mt-4 button-list">
                <NButton secondary round type="info" @click="playApp(item.appUrl)">
                  {{ item.btnText || "立即体验" }}
                </NButton>
                <!-- <n-button  secondary round  type="primary">
				          关注
				          {{ $t('voice.collection') }}
				        </n-button> -->
              </div>
            </NCard>
          </div>
        </main>
      </NTabPane>
      <NTabPane name="socialEntertainment" tab="智链通·科创赋能咨询">
        <main class="flex-1 overflow-hidden" style="margin-left: 20px">
          <div class="card-container">
            <NCard
              v-for="item in tableData"
              :key="item.id"
              :class="getClass(item.type === 'zx')"
              bordered
              hoverable
            >
              <div class="flex justify-between">
                <div>
                  <h3>{{ item.name }}</h3>
                  <p class="ellipsis" :title="item.description">
                    {{ item.description || "——" }}
                  </p>
                </div>
                <!-- <n-avatar :size="48" :src="item.avatar" /> -->
                <NIcon size="48">
                  <img :src="item.avatar" alt="Icon">
                </NIcon>
              </div>
              <NDivider />
              <div class="flex justify-between mt-4 button-list">
                <NButton secondary round type="info" @click="playApp(item.appUrl)">
                  {{ item.btnText || "立即体验" }}
                </NButton>
                <!-- <n-button  secondary round  type="primary">
					          关注
					          {{ $t('voice.collection') }}
					        </n-button> -->
              </div>
            </NCard>
          </div>
        </main>
      </NTabPane>
      <NTabPane name="myFollow" tab="高企·专精特新智能测评">
        <main class="flex-1 overflow-hidden" style="margin-left: 20px">
          <div class="card-container">
            <NCard
              v-for="item in tableData"
              :key="item.id"
              :class="getClass(item.type === 'cp')"
              hoverable
            >
              <div class="flex justify-between">
                <div>
                  <h3>{{ item.name }}</h3>
                  <p class="ellipsis" :title="item.description">
                    {{ item.description || "——" }}
                  </p>
                </div>
                <!-- <n-avatar :size="48" :src="item.avatar" /> -->
                <NIcon size="48">
                  <img :src="item.avatar" alt="Icon">
                </NIcon>
              </div>
              <NDivider />
              <div class="flex justify-between mt-4 button-list">
                <NButton secondary round type="info" @click="playApp(item.appUrl)">
                  {{ item.btnText || "立即体验" }}
                </NButton>
                <!-- <n-button  secondary round  type="primary">
					          关注
					          {{ $t('voice.collection') }}
					        </n-button> -->
              </div>
            </NCard>
          </div>
        </main>
      </NTabPane>
      <NTabPane name="ai" tab="AI智创·项目研发立项助手">
        <main class="flex-1 overflow-hidden" style="margin-left: 20px">
          <div class="card-container">
            <NCard
              v-for="item in tableData"
              :key="item.id"
              :class="getClass(item.type === 'lx')"
              bordered
              hoverable
            >
              <div class="flex justify-between">
                <div>
                  <h3>{{ item.name }}</h3>
                  <p class="ellipsis" :title="item.description">
                    {{ item.description || "——" }}
                  </p>
                </div>
                <!-- <n-avatar :size="48" :src="item.avatar" /> -->
                <NIcon size="48">
                  <img :src="item.avatar" alt="Icon">
                </NIcon>
              </div>
              <NDivider />
              <div class="flex justify-between mt-4 button-list">
                <NButton secondary round type="info" @click="playApp(item.appUrl)">
                  {{ item.btnText || "立即体验" }}
                </NButton>
                <!-- <n-button  secondary round  type="primary">
					          关注
					          {{ $t('voice.collection') }}
					        </n-button> -->
              </div>
            </NCard>
          </div>
        </main>
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped lang="less">
.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .hidden {
    display: none !important;
  }

  /* 修改为左边对齐 */
}

.card-item {
  width: calc(26%);
  margin: 12px;
  border-radius: 10px;
  height: 28vh;
}

.pagination {
  justify-content: center;
  margin-top: 20px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 200px;
  /* Adjust width as needed */
}

.tab-bar {
  margin: 20px 0 20px 20px;
  /* 上下边距20px，左边边距20px */
  padding: 10px;
  /* 添加内边距 */
  border-radius: 8px;
  /* 添加圆角 */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: all 0.3s ease;
  /* 添加动效过渡 */
}
</style>
