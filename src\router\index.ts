import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import { setupPageGuard } from './permission'
import { ChatLayout } from '@/views/chat/layout'
import DifyLayout from '@/views/dify/layout/index.vue'
import mjlayout from '@/views/mj/layout.vue'
import mainLayout from '@/views/dashboard/layout/index.vue'
import agentLayout from '@/views/agent/layout/Layout.vue'
import lumalayout from '@/views/luma/layout.vue'
import storelayout from '@/views/store/layout.vue'
import fanyilayout from '@/views/fanyi/layout.vue'
import pptlayout from '@/views/ppt/layout.vue'
import musiclayout from '@/views/suno/layout.vue'
import knowledgelayout from '@/views/knowledge/layout.vue'
import wxlayout from '@/views/wxbot/layout.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    component: ChatLayout,
    redirect: '/dashboard/index',
    children: [
      {
        path: '/chat/:uuid?',
        name: 'Chat',
        component: () => import('@/views/chat/index.vue'),
      },
    ],
  },
  {
    path: '/dify',
    name: 'Dify',
    component: DifyLayout,
    redirect: '/dify/chat',
    children: [
      {
        path: '/dify/chat',
        name: 'DifyChat',
        component: () => import('@/views/dify/index.vue'),
      },
    ],
  },
  {
    path: '/agent',
    name: 'Rootagent',
    component: agentLayout,
    redirect: '/agent/safe',
    children: [
      {
        path: '/agent/dev',
        name: 'dev',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/safe',
        name: 'safe',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/datasets',
        name: 'datasets',
        component: () => import('@/views/knowledge/index.vue'),
      },
      {
        path: '/agent/employee',
        name: 'employee',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/information',
        name: 'information',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/produce',
        name: 'produce',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/policy',
        name: 'policy',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/market',
        name: 'market',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/manage',
        name: 'manage',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/customer',
        name: 'customer',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/ai',
        name: 'ai',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/fitkc',
        name: 'fitkc',
        component: () => import('@/views/agent/common/chain.vue'),
      },
      {
        path: '/agent/achieve', // 成果转化
        name: 'achieve',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/declare', // 成果转化
        name: 'declare',
        component: () => import('@/views/agent/common/index.vue'),
      },
      {
        path: '/agent/finance', // 财税金融
        name: 'finance',
        component: () => import('@/views/agent/common/index.vue'),
      },
    ],
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: mainLayout,
    redirect: '/dashboard/index',
    children: [
      {
        path: '/dashboard/index',
        name: 'dashboardIndex',
        component: () => import('@/views/dashboard/index.vue'),
      },
    ],
  },
  {
    path: '/g',
    name: 'g',
    component: ChatLayout,
    redirect: '/g/g-2fkFE8rbu',
    children: [
      {
        path: '/g/:gid',
        name: 'GPTs',
        component: () => import('@/views/chat/index.vue'),
      },
    ],
  },

  {
    path: '/m',
    name: 'm',
    component: ChatLayout,
    redirect: '/m/gpt-3.5-turbo',
    children: [
      {
        path: '/m/:gid',
        name: 'Model',
        component: () => import('@/views/chat/index.vue'),
      },
    ],
  },

  {

    path: '/draw',
    name: 'Rootdraw',
    component: mjlayout,
    redirect: '/draw/index',
    children: [
      {
        path: '/draw/:uuid?',
        name: 'draw',
        component: () => import('@/views/mj/draw.vue'),
      },
    ],
  },

  {
    path: '/fanyi',
    name: 'Fanyi',
    component: fanyilayout,
    redirect: '/fanyi/index',
    children: [
      {
        path: 'index',
        name: 'fanyi',
        component: () => import('@/views/fanyi/index.vue'),
      },
    ],
  },

  {
    path: '/ppt',
    name: 'Ppt',
    component: pptlayout,
    redirect: '/ppt/index',
    children: [
      {
        path: 'index',
        name: 'ppt',
        component: () => import('@/views/ppt/index.vue'),
      },
    ],
  },

  {
    path: '/video',
    name: 'Video',
    component: lumalayout,
    redirect: '/video/index',
    children: [
      {
        path: '/video/:uuid?',
        name: 'video',
        component: () => import('@/views/luma/video.vue'),
      },
    ],
  },

  {
    path: '/music',
    name: 'Music',
    component: musiclayout,
    redirect: '/music/index',
    children: [
      {
        path: '/music/:uuid?',
        name: 'music',
        component: () => import('@/views/suno/music.vue'),
      },
    ],
  },

  {
    path: '/store',
    name: 'Store',
    component: storelayout,
    redirect: '/store/t',
    children: [
      {
        path: 't',
        name: 'store',
        component: () => import('@/views/store/appList.vue'),
      },
      {
        path: 'agent',
        name: 'Agent',
        component: () => import('@/views/store/agent.vue'),
      },
    ],
  },

  {
    path: '/wxbot',
    name: 'Wxbot',
    component: wxlayout,
    redirect: '/wxbot/t',
    children: [
      {
        path: 't',
        name: 'wxbot1',
        component: () => import('@/views/wxbot/bot.vue'),
      },
    ],
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: knowledgelayout,
    redirect: '/knowledge/t',
    children: [
      {
        path: 't',
        name: 'knowledge1',
        component: () => import('@/views/knowledge/index.vue'),
      },
    ],
  },

  {
    path: '/annex',
    name: 'Annex',
    component: knowledgelayout,
    redirect: '/annex/t',
    children: [
      {
        path: 't',
        name: 'annex1',
        component: () => import('@/views/knowledge/annex.vue'),
      },
    ],
  },

  {
    path: '/fragment',
    name: 'Fragment',
    component: knowledgelayout,
    redirect: '/fragment/t',
    children: [
      {
        path: 't',
        name: 'fragment1',
        component: () => import('@/views/knowledge/fragment.vue'),
      },
    ],
  },

  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404/index.vue'),
  },

  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
  },

  {
    path: '/regist',
    name: 'regist',
    component: () => import('@/views/regist/index.vue'),
  },
  {
    path: '/resetpassword',
    name: 'resetpassword',
    component: () => import('@/views/resetpassword/index.vue'),
  },

  {
    path: '/500',
    name: '500',
    component: () => import('@/views/exception/500/index.vue'),
  },

  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/404',
  },
]

export const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
})

setupPageGuard(router)

export async function setupRouter(app: App) {
  app.use(router)
  await router.isReady()
}
