html,
body,
#app {
	height: 100%;
}

body {
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.active {
	color: #2254F4 !important;
}

.theme-bg-color {
	background-color: #EDF0FF !important;
}

.message-text {
	hr {
		margin: 0.5rem 0;
	}
}

// 若样式未生效，可能是由于选择器优先级问题、样式被覆盖，或者未正确使用 :deep() 穿透。
// 以下保留原选择器，添加样式规则确保链接样式生效
.message-text :deep(p:last-child) {
	margin-bottom: 0;
}

.message-text :deep(a) {
	color: #155aef;
	text-decoration: none;
}

.message-text :deep(pre) {
	background-color: #f4f4f5;
	border-radius: 0.375rem;
	padding: 16px;
	overflow-x: auto;
	margin: 12px 0;
	border: 1px solid #e4e4e7;
}

.message-text :deep(code) {
	font-family: "Fira Code", "SFMono-Regular", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, monospace;
	background-color: #f4f4f5;
	padding: 2px 4px;
	border-radius: 0.25rem;
	font-size: 0.9em;
}

.message-text :deep(ul),
.message-text :deep(ol) {
	margin: 8px 0;
	padding-left: 24px;
}