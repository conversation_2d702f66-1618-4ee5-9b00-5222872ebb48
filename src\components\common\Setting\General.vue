<script lang="ts" setup>
import { computed, ref } from 'vue'
import type { UploadFileInfo } from 'naive-ui'
import {
  NButton,
  NInput,
  NRadio,
  NRadioGroup,
  NSpace,
  NUpload,
  useMessage,
} from 'naive-ui'
import to from 'await-to-js'
import type { Language, Theme } from '@/store/modules/app/helper'
import { useAppStore, useUserStore } from '@/store'
import { getToken } from '@/store/modules/auth/helper'
import { eidtProfile, getUserInfo } from '@/api/user'

const message = useMessage()
const appStore = useAppStore()
const userStore = useUserStore()

const ms = useMessage()

const theme = computed(() => appStore.theme)

const userInfo = computed(() => userStore.userInfo)
const baseUrl = ref('')

console.log('userInfo-----', userInfo.value)

const name = ref(userInfo.value.name ?? '')

const language = computed({
  get() {
    return appStore.language
  },
  set(value: Language) {
    appStore.setLanguage(value)
  },
})
baseUrl.value = import.meta.env.VITE_GLOB_API_URL

const themeOptions: { label: string; key: Theme; icon: string }[] = [
  {
    label: 'Auto',
    key: 'auto',
    icon: 'ri:contrast-line',
  },
  {
    label: 'Light',
    key: 'light',
    icon: 'ri:sun-foggy-line',
  },
  {
    label: 'Dark',
    key: 'dark',
    icon: 'ri:moon-foggy-line',
  },
]

const languageOptions: { label: string; key: Language; value: Language }[] = [
  { label: '简体中文', key: 'zh-CN', value: 'zh-CN' },
  { label: 'English', key: 'en-US', value: 'en-US' },
]
const paramKey = {
  nickName: '昵称',
  sex: '性别',
  email: '邮箱',
  phonenumber: '手机号',
  userId: '用户id',
}
function updateUserInfo(e: MouseEvent) {
  // userStore.updateUserInfo(options);
  e.preventDefault()
  const params = {
    nickName: userInfo.value.nickName,
    sex: userInfo.value.sex,
    email: userInfo.value.email,
    phonenumber: userInfo.value.phonenumber,
    userId: userInfo.value.userId,
  }
  // 遍历 params 对象检查是否有空值
  for (const [key, value] of Object.entries(params)) {
    if (value === null || value === undefined || value === '') {
      ms.error(`${paramKey[key]} 不能为空`)
      return
    }
  }
  eidtProfile(params)
    .then((res) => {
      ms.success(res.msg)
      getLoginUserInfo()
    })
    .catch((err) => {
      console.log(err)
      ms.error(err)
    })
  // ms.success(t("common.success"));
}
async function getLoginUserInfo() {
  // 用户未登录,不需要获取用户信息
  if (!getToken())
    return
  console.log('getLoginUserInfo')
  const [err, newUserInfo] = await to(getUserInfo())
  if (err) {
    // message.error(err.toString())
    console.log(err.toString())
  }

  if (newUserInfo) {
    const userStore = useUserStore()
    userStore.updateUserInfo(newUserInfo.data.user)
  }
}

const fileList = ref<UploadFileInfo[]>([
  {
    id: 'avatar',
    name: '头像',
    status: 'finished',
    url: userInfo.value.avatar
      ? userInfo.value.avatar
      : 'http://panda-1253683406.cos.ap-guangzhou.myqcloud.com/panda/2024/01/03/0e3600b455914b0dade9943f281be19b.png',
  },
])

const token = getToken()
const headers = {
  Authorization: `Bearer ${token}`,
}

function handleFinish({ event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  const ext = (event?.target as XMLHttpRequest).response
  fileList.value[0].url = ext
  message.success('上传成功！')
}
</script>

<template>
  <div class="p-4 space-y-5 min-h-[200px]">
    <div class="space-y-6">
      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px] text-right pr-5">{{ $t("setting.avatarLink") }} <span class="text-red-600">*</span></span>
        <NUpload
          :action="`${baseUrl}/system/user/edit/avatar`"
          :max="1"
          list-type="image-card"
          :default-file-list="fileList"
          :headers="headers"
          @finish="handleFinish"
        >
          点击上传
        </NUpload>
      </div>

      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px] text-right pr-5">昵称 <span class="text-red-600">*</span></span>
        <div class="w-[270px]">
          <NInput v-model:value="userInfo.nickName" placeholder="" />
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px] text-right pr-5">邮箱 <span class="text-red-600">*</span></span>
        <div class="w-[270px]">
          <NInput v-model:value="userInfo.email" placeholder="" />
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px] text-right pr-5">性别 <span class="text-red-600">*</span></span>
        <div class="w-[270px]">
          <!-- <NInput v-model:value="userInfo.sex" placeholder="" /> -->
          <NRadioGroup v-model:value="userInfo.sex" name="radiogroup">
            <NSpace>
              <NRadio key="0" value="0">
                男
              </NRadio>
              <NRadio key="1" value="1">
                女
              </NRadio>
            </NSpace>
          </NRadioGroup>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px] text-right pr-5">电话 <span class="text-red-600">*</span></span>
        <div class="w-[270px]">
          <NInput v-model:value="userInfo.phonenumber" placeholder="" />
        </div>
      </div>

      <!-- <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px]">{{ $t('setting.theme') }}</span>
        <div class="flex flex-wrap items-center gap-4">
          <template v-for="item of themeOptions" :key="item.key">
            <NButton
              size="small"
              :type="item.key === theme ? 'primary' : undefined"
              @click="appStore.setTheme(item.key)"
            >
              <template #icon>
                <SvgIcon :icon="item.icon" />
              </template>
            </NButton>
          </template>
        </div>
      </div> -->
      <!-- <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px]">{{ $t("setting.language") }}</span>
        <div class="flex flex-wrap items-center gap-4">
          <NSelect
            style="width: 140px"
            :value="language"
            :options="languageOptions"
            @update-value="(value) => appStore.setLanguage(value)"
          />
        </div>
      </div> -->
      <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[100px]" />
        <div class="flex flex-wrap items-center gap-4">
          <NButton type="primary" @click="updateUserInfo">
            更新信息
          </NButton>
        </div>
      </div>
    </div>
  </div>
</template>
