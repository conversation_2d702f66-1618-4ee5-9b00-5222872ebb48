<script setup lang="ts">
import type { FormInst, FormItemInst, FormItemRule, FormRules } from 'naive-ui'
import { NButton, NCol, NForm, NFormItem, NInput, NRow, useMessage } from 'naive-ui'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { loginOut, updatePwd } from '@/api/user'
import { removeToken } from '@/store/modules/auth/helper'

interface ModelType {
  oldPassword: string | null
  newPassword: string | null
  reenteredPassword: string | null
}
const router = useRouter()
const formRef = ref<FormInst | null>(null)
const rPasswordFormItemRef = ref<FormItemInst | null>(null)
const message = useMessage()
const modelRef = ref<ModelType>({
  oldPassword: null,
  newPassword: null,
  reenteredPassword: null,
})
//       ,
const model: typeof modelRef = modelRef
const rules: FormRules = {
  oldPassword: [
    {
      required: true,
      validator(rule: FormItemRule, value: string) {
        if (!value)
          return new Error('请输入原密码')
        // else if (!/^\d*$/.test(value))
        //   return new Error('年龄应该为整数')
        // else if (Number(value) < 18)
        //   return new Error('年龄应该超过十八岁')

        return true
      },
      trigger: ['input', 'blur'],
    },
  ],
  newPassword: [
    {
      required: true,
      message: '请输入新密码',
    },
  ],
  reenteredPassword: [
    {
      required: true,
      message: '请再次输入新密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次密码输入不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
}

function validatePasswordStartWith(rule: FormItemRule, value: string): boolean {
  return (
    !!modelRef.value.newPassword
    && modelRef.value.newPassword.startsWith(value)
    && modelRef.value.newPassword.length >= value.length
  )
}
function validatePasswordSame(rule: FormItemRule, value: string): boolean {
  return value === modelRef.value.newPassword
}
function handlePasswordInput() {
  if (modelRef.value.reenteredPassword)
    rPasswordFormItemRef.value?.validate({ trigger: 'password-input' })
}
function handleValidateButtonClick(e: MouseEvent) {
  e.preventDefault()
  formRef.value?.validate((errors) => {
    if (!errors) {
      updatePwd(modelRef.value).then(async (res) => {
        message.success(res.msg)
        await loginOut()
        removeToken()
        // 跳转到登录页面
        router.push('/login')
      })
    }
    else {
      console.log(errors)
      //   message.error('验证失败')
    }
  })
}

// export default defineComponent({
//   setup() {

//     return {
//       formRef,
//       rPasswordFormItemRef,
//       model: modelRef,
//       rules,
//       handlePasswordInput() {
//         if (modelRef.value.reenteredPassword)
//           rPasswordFormItemRef.value?.validate({ trigger: 'password-input' })
//       },
//
//     }
//   },
// })
</script>

<template>
  <div class="w-2/3 mt-10">
    <NForm
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      label-width="120px"
    >
      <NFormItem path="oldPassword" label="原密码">
        <NInput
          v-model:value="model.oldPassword"
          type="password"
          @keydown.enter.prevent
        />
      </NFormItem>
      <NFormItem path="newPassword" label="新密码">
        <NInput
          v-model:value="model.newPassword"
          type="password"
          @input="handlePasswordInput"
          @keydown.enter.prevent
        />
      </NFormItem>
      <NFormItem
        ref="rPasswordFormItemRef"
        first
        path="reenteredPassword"
        label="确认新密码"
      >
        <NInput
          v-model:value="model.reenteredPassword"
          :disabled="!model.newPassword"
          type="password"
          @keydown.enter.prevent
        />
      </NFormItem>
      <NRow :gutter="[0, 24]">
        <NCol :span="12">
          <div style="display: flex; justify-content: flex-end">
            <NButton
              :disabled="model.oldPassword === null"
              type="primary"
              @click="handleValidateButtonClick"
            >
              修改密码
            </NButton>
          </div>
        </NCol>
      </NRow>
    </NForm>
  </div>

  <!-- <pre>{{ JSON.stringify(model, null, 2) }} -->
  <!-- </pre> -->
</template>
