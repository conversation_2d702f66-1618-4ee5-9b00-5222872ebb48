<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
const src = ref('')

onMounted(() => {
  src.value = decodeURIComponent(route.query.href)
})
</script>

<template>
  <div style="background-color: #eee">
    <iframe
      id="assessId"
      :src="src"
      width="100%"
      height="100%"
      frameborder="0"
      allow="microphone"
      class="iframe-wrapper"
    />
  </div>
</template>

<style lang="less" scoped>
.iframe-wrapper {
  height: 100%;
}
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}
</style>
