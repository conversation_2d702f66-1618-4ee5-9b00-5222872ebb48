import request from '@/utils/request/req'

const baseUrl = import.meta.env.VITE_AI_API_BASE
const datasetsKey = import.meta.env.VITE_DATASETS_API_KEY

// 获取知识库信息
export function getDatasetsInfo(datasetsId: string, data: any) {
  return request({
    url: `${baseUrl}/datasets/${datasetsId}`,
    method: 'get',
    params: data,
    headers: {
      isToken: false,
      Authorization: `Bearer ${datasetsKey}`,
    },
  })
}

// 获取知识库内文档列表
export function getDocList(datasetsId: string, data: any) {
  return request({
    url: `${baseUrl}/datasets/${datasetsId}/documents`,
    method: 'get',
    params: data,
    headers: {
      isToken: false,
      Authorization: `Bearer ${datasetsKey}`,
    },
  })
}
// 删除知识库内文档
export function delDoc(datasetsId: string, docId: any) {
  return request({
    url: `${baseUrl}/datasets/${datasetsId}/documents/${docId}`,
    method: 'delete',
    headers: {
      isToken: false,
      Authorization: `Bearer ${datasetsKey}`,
    },
  })
}
