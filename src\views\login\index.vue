<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { NImage, NModal, useMessage } from 'naive-ui'
import { ElCol, ElRow } from 'element-plus'
import to from 'await-to-js'
import { useI18n } from 'vue-i18n'
import type { LoginFrom } from '@/typings/user'
import { getLoginType, getMpQrCode, selectByDomain } from '@/api/user'
import { useUserStore } from '@/store/modules/user'
import { useTenantStore } from '@/store/modules/tenant'
import { useBasicLayout } from '@/hooks/useBasicLayout'

// 租户logo
const tenantLogo = ref('')
const { isMobile } = useBasicLayout()
const { t } = useI18n()

const userStore = useUserStore()
const tenantStore = useTenantStore()
const router = useRouter()
const message = useMessage()
const user = ref<LoginFrom>(Object.create(null))

// 点击登录
const loginLoading = ref(false)
async function handleValidateButtonClick(e: MouseEvent) {
  e.preventDefault()
  const { username, password } = user.value
  // if (!validateAccount(username)) {
  // 	message.error(t("login.accountFormatError"));
  // 	return;
  // }
  if (username && password) {
    loginLoading.value = true
    const [err] = await to(userStore.userLogin(user.value))
    if (!err) {
      message.success(t('login.loginSuccess'))
      await router.push('/')
      loginLoading.value = false
    }
    else {
      message.error(err.message)
      loginLoading.value = false
    }
  }
  else {
    message.error(t('login.usernameOrPasswordEmpty'))
  }
}

function validateAccount(account: string) {
  if (!account)
    return false

  const phoneRegex = /^1[3456789]\d{9}$/
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(account) || phoneRegex.test(account)
}

const handleRegistBtnClick = async (e: MouseEvent) => {
  router.push('/regist')
}

const copyright = ref('')

const activate = ref(false)

const code = ref('')

// 获取当前域名或IP
function getCurrentDomain() {
  return window.location.hostname
}

// 在组件挂载后执行异步操作
onMounted(async () => {
  // 获取当前域名
  const domain = getCurrentDomain()

  // 调用 selectByDomain 接口
  const [err, res] = await to(selectByDomain(domain))
  if (err) {
    console.error('获取域名配置失败', err.message)
  }
  else {
    console.log('域名配置信息:', res)
    // 保存租户信息到store
    if (res?.data) {
      tenantStore.updateTenantInfo(res.data)
      // 设置租户logo
      tenantLogo.value = res.data.tenantLogo
    }
  }

  // const [err, res] = await to(getConfigKey('copyright'))
  // if (err)
  //   console.error('获取配置失败', err.message)
  // else copyright.value = res && res?.msg
})

const activeTab = ref('login')

const showModal = ref(false)

// 登录二维码
const qrCode = ref('')
const ticket = ref('')

let intervalId: string | number | NodeJS.Timer | undefined
// 定义轮询间隔时间，例如每3秒轮询一次
const POLLING_INTERVAL = 3000

async function handleWxLogin() {
  showModal.value = true
  // 获取二维码信息
  const [err1, res1] = await to(getMpQrCode())
  if (err1) {
    message.error(`获取二维码失败: ${err1.message}`)
  }
  else {
    qrCode.value = res1.data.qrCodeUrl
    ticket.value = res1.data.ticket
    intervalId = setInterval(slectLoginType, POLLING_INTERVAL)
  }
}

// 1. 定时查询是否登录成功
async function slectLoginType() {
  const [err, res] = await to(getLoginType(ticket.value))
  if (!err) {
    if (res.data.token) {
      // 2. 登录成功,保存token
      userStore.userQrLogin(res.data.token)
      clearInterval(intervalId)
      // 3. 跳转到主页
      message.success(t('login.loginSuccess'))
      await router.push('/')
    }
  }
}

onUnmounted(() => {
  // 页面组件卸载前清除定时器，避免内存泄漏
  if (intervalId !== undefined)
    clearInterval(intervalId)
})
</script>

<template>
  <div id="app">
    <div class="relative w-full bg-white overflow-hidden login-box">
      <div class="login-left">
        <div class="ad-text">
          <h2>FIT飞地科服AI智脑</h2>
          <p>企业级全场景数智化决策中枢，私有化大模型+多角色Agent驱动商业价值跃升。</p>
          <div class="ad-tip">
            <ElRow>
              <ElCol :span="8">
                <div class="title">
                  优选大模型私有化部署
                </div>
                <div class="subtitle">
                  企业数据安全的智能底座
                </div>
              </ElCol>
              <ElCol :span="8">
                <div class="title">
                  数智员工矩阵
                </div>
                <div class="subtitle">
                  全岗位AI Agent协同提效
                </div>
              </ElCol>
              <ElCol :span="8">
                <div class="title">
                  知识库 · 情报感知
                </div>
                <div class="subtitle">
                  商业决策的智慧双引擎
                </div>
              </ElCol>
            </ElRow>
            <ElRow class="mt-10">
              <ElCol :span="8">
                <div class="title">
                  研发管理 · 政策支持
                </div>
                <div class="subtitle">
                  创新合规双轮驱动
                </div>
              </ElCol>
              <ElCol :span="8">
                <div class="title">
                  营销推广 · 项目管理
                </div>
                <div class="subtitle">
                  增长闭环的智能推手
                </div>
              </ElCol>
              <ElCol :span="8">
                <div class="title">
                  智能客服 · AI工具箱
                </div>
                <div class="subtitle">
                  引领用户体验与创意生产的革新
                </div>
              </ElCol>
            </ElRow>
          </div>
          <img src="@/assets/img/login-bg-3.png" alt="login" class="login-img-3">
        </div>

        <div class="footer">
          <a target="_blank" style="font-size: 14px" href="https://fitkc.cn">
            Copyright © {{ new Date().getFullYear() }} 湖南飞地科技创业有限公司
          </a>
        </div>
      </div>
      <div class="login-right">
        <main class="mx-auto" style="padding-top: 100px">
          <div v-if="activeTab === 'login'">
            <!-- 登录表单 -->
            <div class="flex flex-col justify-center my-4 space-y-8">
              <div class="mx-auto w-full max-w-md">
                <img
                  v-if="tenantLogo"
                  :src="tenantLogo || '@/assets/img/logo-2.png'"
                  alt="login"
                  width="230px"
                  class="mx-auto mb-5 logo-img"
                >
                <img
                  v-else
                  src="@/assets/img/logo-2.png"
                  alt="login"
                  width="230px"
                  class="mx-auto mb-5 logo-img"
                >
                <!-- <h2 class="text-3xl font-bold text-center text-gray-900">
                  {{ $t("login.login") }}
                </h2> -->
                <!-- <p class="mt-2 text-sm text-center text-gray-600 login-desc">
                  {{ $t("login.or") }}
                  <a
                    class="font-semibold text-teal-500 hover:text-teal-600 cursor-pointer"
                    @click="handleRegistBtnClick"
                  >{{ $t("login.register") }}</a>
                  {{ $t("login.andExperience") }}
                </p> -->
              </div>
              <div class="mx-auto w-full max-w-sm">
                <form class="space-y-6">
                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">{{
                      $t("login.emailOrPhone")
                    }}</label>
                    <div class="mt-1">
                      <input
                        id="email"
                        v-model="user.username"
                        :allow-input="(val: string) => { return !/[^A-Za-z0-9_@.]/g.test(val) }"
                        maxlength="32"
                        :placeholder="$t('login.enterEmailOrPhone')"
                        name="email"
                        type="email"
                        autocomplete="email"
                        required
                        class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md focus:border-teal-500 focus:outline-none focus:ring-teal-500"
                      >
                    </div>
                  </div>
                  <div>
                    <label
                      for="password"
                      class="block text-sm font-medium text-gray-700"
                    >{{ $t("login.password") }}</label>
                    <div class="mt-1">
                      <input
                        id="password"
                        v-model="user.password"
                        maxLength="16"
                        :placeholder="$t('login.enterPassword')"
                        name="password"
                        type="password"
                        required
                        class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md focus:border-teal-500 focus:outline-none focus:ring-teal-500"
                      >
                    </div>
                  </div>
                  <div class="footer-login">
                    <button :loading="loginLoading" @click="handleValidateButtonClick">
                      登录
                    </button>
                    <a
                      style="color: #0084ff; font-weight: 500"
                      href="/regist"
                      class="float-right mt-2 text-sm font-semibold text-teal-500 hover:text-teal-600"
                    >{{ "没有账号？去注册" }}</a>
                    <!-- <n-button @click="handleWxLogin">微信登录</n-button> -->
                    <img width="135" src="@/assets/img/login-img-4.png">
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 扫码登录 -->
          <NModal
            v-model:show="showModal"
            title="请扫描下方二维码登录"
            preset="card"
            draggable
            :style="{ width: '400px' }"
          >
            <NImage width="350" :src="qrCode" />
          </NModal>
        </main>
      </div>
    </div>

    <!-- <div v-if="!activate" id="specialDiv">
			<p>
				{{ $t("login.systemNotActivated") }}
				<n-button size="small" secondary strong @click="showModal = true">{{
					$t("login.activate")
				}}</n-button>
			</p>
		</div> -->
  </div>
  <!-- <n-modal v-model:show="showModal">
		<n-card
			style="width: 600px"
			:bordered="false"
			size="huge"
			role="dialog"
			aria-modal="true"
		>
			<p class="text-sm text-center" style="margin-right: 10px">
				{{ $t("login.enterAuthCode") }}
			</p>
			<br />
			<div style="display: flex; align-items: center; justify-content: center">
				<input
					id="code"
					v-model="code"
					maxlength="32"
					:placeholder="$t('login.activationCode')"
					class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md focus:border-teal-500 focus:outline-none focus:ring-teal-500"
				/>
				<n-button style="margin-left: 10px" @click="sysAuth()">{{
					$t("login.activate")
				}}</n-button>
			</div>
		</n-card>
	</n-modal> -->
</template>

<style scoped lang="less">
#app {
  height: 100vh;
  overflow: hidden;
}
.login-left {
  background: url("@/assets/img/login-bg-1.png") no-repeat center center;
  width: 65%;
  height: 100vh;
  float: left;
  position: relative;
  .ad-text {
    h2 {
      font-weight: bold;
      font-size: 72px;
      color: #ffffff !important;
      line-height: 138px;
      text-shadow: 0px 9px 0px rgba(19, 61, 121, 0.16);
      text-align: center;
      margin-top: 120px;
    }
    p {
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      line-height: 36px;
      text-align: center;
    }
    .login-img-3 {
      margin: 50px auto 0 auto;
      width: 180px;
    }
  }
  .ad-tip {
    width: 820px;
    margin: 0 auto;
    margin-top: 80px;
    color: #ffffff;
    ::v-deep .el-col {
      padding-left: 35px;
      position: relative;
      // border-right: 1px solid;
      // border-image: radial-gradient(to bottom, #6badf3, #2882f7) 1;
      // &::after {
      //   position: absolute;
      //   width: 1px;
      //   height: 100%;
      //   background: linear-gradient(to bottom, #66bef5, #66bef5);
      //   top: 0;
      //   right: 0;
      // }
    }
    .title {
      font-weight: bold;
      font-size: 20px;
      color: #ffffff;
      line-height: 36px;
    }
    .subtitle {
      font-size: 14px;
      color: #e3e3e3;
      line-height: 36px;
    }
  }
}
.login-right {
  background: url("@/assets/img/login-bg-2.png") no-repeat top right;
  width: 35%;
  height: 100%;
  float: right;
}

.footer {
  position: absolute;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  margin-top: auto;
  bottom: 20px;
  a {
    color: #fff;
  }
}

.custom-card {
  width: 500px;
}

input {
  color: black !important;
}

#specialDiv {
  position: relative;
  /* 使内部的绝对定位元素相对定位 */
}

#specialDiv p {
  position: absolute;
  /* 绝对定位 */
  bottom: 0;
  /* 将元素对齐到底部 */
  right: 0;
  /* 将元素对齐到右边 */
  margin: 0;
  /* 去除默认的段落外边距 */
}
.footer-login {
  padding-bottom: 40px;
  img {
    text-align: center;
    margin: 50px auto 0 auto;
  }
}
.logo-img {
  max-height: 100px;
}
</style>
