import { ss } from '@/utils/storage'

const LOCAL_NAME = 'appSetting'

export type Theme = 'light' | 'dark' | 'auto'

export type Language = 'zh-CN' | 'zh-TW' | 'en-US' | 'ko-KR' | 'ru-RU'

export interface AppState {
  siderCollapsed: boolean
  isChat: boolean
  theme: Theme
  language: Language
}

// 二级菜单
export interface SubMenuData {
  component: string
  hidden: boolean
  meta: { title: string; icon: string; noCache: boolean; link: string | null }
  name: string
  path: string
  query: string
}

export interface subMenuState {
  subMenuData: [SubMenuData]
}

export function defaultSubMenuData(): subMenuState {
  return {
    subMenuData: [
      {
        component: 'agent',
        hidden: false,
        meta: { title: '智谱AI', icon: 'icon-zhipuAI', noCache: false, link: null },
        name: 'chat/VebWgnoJ7viuKW4s',
        path: 'chat/VebWgnoJ7viuKW4s',
        query: '{"type": "agent","code": "VebWgnoJ7viuKW4s" }',
      },
    ],
  }
}

export function defaultSetting(): AppState {
  return { siderCollapsed: false, isChat: true, theme: 'light', language: 'zh-CN' }
}

export function getLocalSetting(): AppState {
  const localSetting: AppState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalSetting(setting: AppState): void {
  ss.set(LOCAL_NAME, setting)
}
