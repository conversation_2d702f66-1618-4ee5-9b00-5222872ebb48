export function getCurrentDate() {
  const date = new Date()
  const day = date.getDate()
  const month = date.getMonth() + 1
  const year = date.getFullYear()
  return `${year}-${month}-${day}`
}

export function formatFileSize(size: number) {
  if (!size)
    return
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  // 格式化为两位小数，并去除末尾的零
  const formattedSize = size.toFixed(2).replace(/\.?0+$/, '')
  return `${formattedSize}${units[unitIndex]}`
}

export function getFileType(type) {
  const document = ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB']
  const image = ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG']
  const audio = ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR']
  const video = ['MP4', 'MOV', 'MPEG', 'MPGA']
  const upperType = type.toUpperCase()
  if (image.includes(upperType))
    return 'image'
  else if (audio.includes(upperType))
    return 'audio'
  else if (document.includes(upperType))
    return 'document'
  else if (video.includes(upperType))
    return 'video'

  return 'custom' // 如果类型不在任何数组中，返回null
}
