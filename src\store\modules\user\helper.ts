import { ss } from '@/utils/storage'

const LOCAL_NAME = 'userStorage'

export interface UserInfo {
  avatar: string
  name: string
  userBalance: number
  userGrade: string
  userName: string
  email: string
  nickName: string
  phonenumber: string
  sex: string
  userId: number | null
}

export interface UserState {
  userInfo: UserInfo
  roles: []
}

export function defaultSetting(): UserState {
  return {
    userInfo: {
      avatar: 'https://avatars.githubusercontent.com/u/32251822?v=4',
      name: '熊猫助手',
      userBalance: 0,
      userGrade: '0',
      email: '',
      nickName: '',
      phonenumber: '',
      sex: '0',
      userId: null,
    },
    roles: [],
  }
}

export function getLocalState(): UserState {
  const localSetting: UserState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: UserState): void {
  ss.set(LOCAL_NAME, setting)
}
