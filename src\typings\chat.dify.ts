// 消息类型定义
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  thoughts?: AgentThought[] // Agent思考过程
  files?: MessageFile[] // 消息附件，如图片
  agent_thoughts?: AgentThought[] // Agent思考过程
  message_files?: MessageFile[] // 消息附件，如图片
  isStreaming?: boolean // 是否正在流式传输
  query?: string // 用户输入的查询
  conversation_id?: string // 对话ID
  answer?: string // 助手的回答
  retriever_resources?: any[] // 检索器资源
  message_id?: string // 消息ID
}

// Agent思考过程
export interface AgentThought {
  id: string
  position: number
  thought: string
  observation: string
  tool?: string
  tool_input?: string
  message_files?: string[]
}

// 消息文件（如图片）
export interface MessageFile {
  id: string
  type: string
  url: string
  belongs_to: 'user' | 'assistant'
}

// 音频响应类型
export interface AudioResponse {
  audio: Blob
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data: T // 移除可选标记，因为成功时一定有数据
  error?: string
}

// 聊天请求参数
export interface ChatRequest {
  query: string
  conversation_id?: string | null
  user_id: string
}

// 聊天响应
export interface ChatResponse {
  message_id: string
  conversation_id: string
  content: string
}
