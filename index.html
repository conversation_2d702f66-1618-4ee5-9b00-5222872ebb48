<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
	<meta charset="UTF-8">
	<meta name="keywords" content="AI,FIT飞地科服,AI智脑,FIT飞地科创,飞地科服, 科技服务,科服AI,AI智脑,科企服务AI智脑,飞地科服AI智脑,FIT科企智脑">
	<meta name="author" content="FIT飞地科服AI智脑">
	<meta name="description"
		content="飞地科服AI智脑专为科技服务专家（技术经理人、科研财务、金融、知识产权代理人等）及企业科研管理、财务人才打造。它深度融合AI能力、知识库与智能体平台，提供涵盖优选大型模型、私有化知识库、数智员工、科技服务行业智能体及其他提效工具于一体的智能中台。旨在构建企业科技创新全链路专业数智化平台，赋能科技服务高效协同与智能决策。">
	<!-- <link rel="apple-touch-icon" href="/favicon.ico"> -->
	<link rel="icon" href="/favicon.ico" />
	<meta name="viewport"
		content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
	<title>FIT飞地科服AI智脑</title>
</head>

<body class="dark:bg-black">
	<div id="app">
		<style>
			.loading-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100vh;
			}

			.balls {
				width: 4em;
				display: flex;
				flex-flow: row nowrap;
				align-items: center;
				justify-content: space-between;
			}

			.balls div {
				width: 0.8em;
				height: 0.8em;
				border-radius: 50%;
				background-color: #4b9e5f;
			}

			.balls div:nth-of-type(1) {
				transform: translateX(-100%);
				animation: left-swing 0.5s ease-in alternate infinite;
			}

			.balls div:nth-of-type(3) {
				transform: translateX(-95%);
				animation: right-swing 0.5s ease-out alternate infinite;
			}

			@keyframes left-swing {

				50%,
				100% {
					transform: translateX(95%);
				}
			}

			@keyframes right-swing {
				50% {
					transform: translateX(-95%);
				}

				100% {
					transform: translateX(100%);
				}
			}

			@media (prefers-color-scheme: dark) {
				body {
					background: #121212;
				}
			}
		</style>
		<div class="loading-wrap">
			<div class="balls">
				<div></div>
				<div></div>
				<div></div>
			</div>
		</div>
	</div>
	<script type="module" src="/src/main.ts"></script>
</body>

</html>