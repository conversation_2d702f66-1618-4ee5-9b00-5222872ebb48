<script setup lang="ts">
import { computed, onUnmounted, ref } from 'vue'
import { ElAvatar, ElButton, ElIcon, ElMessage, ElTooltip } from 'element-plus'
import {
  ChatRound,
  DocumentCopy,
  Microphone,
  RefreshLeft,
  SetUp,
  UserFilled,
  VideoPause,
} from '@element-plus/icons-vue'
import { marked } from 'marked'
import type { Message } from '../../../typings/chat.dify'
import { chatApi } from './../../../api/difychat'
import { useUserStore } from '@/store'
import { formatFileSize } from '@/utils/functions/index'
const props = defineProps<{
  message: Message
}>()
const emit = defineEmits<{
  (e: 'send', value: string): void
}>()
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 是否显示思考过程
const showThoughts = ref(true)

// 切换思考过程显示/隐藏
const toggleThoughts = () => {
  showThoughts.value = !showThoughts.value
}

// 是否有思考过程
const hasThoughts = computed(() => {
  return props.message.agent_thoughts && props.message.agent_thoughts.length > 0
})

// 跟踪内容是否有变化
const contentChanged = ref(false)

// 监听消息内容变化
// watch(
//   () => props.message.content,
//   (newContent, oldContent) => {
//     if (newContent !== oldContent) {
//       contentChanged.value = true
//       // 3秒后重置动画标志
//       setTimeout(() => {
//         contentChanged.value = false
//       }, 1000)
//     }
//   },
// )

// 按位置排序的思考过程
const sortedThoughts = computed(() => {
  if (!props.message.agent_thoughts)
    return []
  return [...props.message.agent_thoughts].sort((a, b) => a.position - b.position)
})

// 格式化工具输入参数
const formatToolInput = (input: string) => {
  try {
    const parsed = JSON.parse(input)
    return JSON.stringify(parsed, null, 2)
  }
  catch (e) {
    return input
  }
}

// Computed properties
const messageClass = computed(() => [
  `message-${props.message.role}`,
  { 'is-streaming': props.message.isStreaming },
  { 'content-changed': contentChanged.value },
])

const avatarIcon = computed(() => {
  return props.message.role === 'user' ? UserFilled : ChatRound
})

const roleLabel = computed(() => {
  return props.message.role === 'user' ? '您' : 'AI 助手'
})

// 安全渲染内容
const safedContent = (content: any) => {
  if (!content)
    return ''

  try {
    // 尝试使用marked处理Markdown
    return marked(content, { breaks: true })
  }
  catch (err) {
    console.error('Markdown解析错误:', err)

    // 如果解析失败，返回简单的HTML转义版本
    return escapeHtml(content)
  }
}

// HTML转义函数
function escapeHtml(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .replace(/\n/g, '<br>')
}

const formattedTime = computed(() => {
  const date = new Date(props.message.timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
})

// 音频播放相关状态
const isPlaying = ref(false)
const isLoading = ref(false)
const audioPlayer = ref<HTMLAudioElement | null>(null)

// 播放音频
const playAudio = async () => {
  if (!props.message.content)
    return

  try {
    isLoading.value = true
    const response = await chatApi.textToAudio({
      text: props.message.content,
      user: userInfo.value?.userId,
    })

    if (response.success) {
      const audioUrl = URL.createObjectURL(response.data.audio)

      // 创建或重用音频播放器
      if (!audioPlayer.value) {
        audioPlayer.value = new Audio()
        audioPlayer.value.onended = () => {
          isPlaying.value = false
        }
      }

      audioPlayer.value.src = audioUrl
      await audioPlayer.value.play()
      isPlaying.value = true
    }
    else {
      console.error('获取音频失败:', response.error)
      // 添加用户友好的错误提示
      ElMessage.error('语音功能暂未启用，请联系管理员开启TTS功能')
    }
  }
  catch (error) {
    console.error('播放音频时出错:', error)
    // 添加用户友好的错误提示
    ElMessage.error('语音功能暂未启用，请联系管理员开启TTS功能')
  }
  finally {
    isLoading.value = false
  }
}

// 停止音频播放
const stopAudio = () => {
  if (audioPlayer.value) {
    audioPlayer.value.pause()
    audioPlayer.value.currentTime = 0
    isPlaying.value = false
  }
}
const copyMessage = (message: any) => {
  if (message?.answer) {
    try {
      // 检查 navigator.clipboard 是否可用
      if (navigator.clipboard) {
        // ElMessage.error('复制失败，当前环境不支持剪贴板功能')
        navigator.clipboard.writeText(message.answer)
        ElMessage.success('复制成功')
      }
      else {
        const textarea = document.createElement('textarea')
        textarea.value = message?.answer
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        ElMessage.success('复制成功')
      }
    }
    catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请重试')
    }
  }
}

const refreshMessage = (message: any) => {
  emit('send', message.query, message.message_files)
}

const feedbacksFuc = (message: any, type: any) => {
  const param = {
    user: userInfo.value?.userId,
    message_id: message.id,
    rating: type,
    content: message.content,
  }
  chatApi.feedbacksMes(message.id, param).then((res) => {
    if (res.success)
      ElMessage.success('操作成功')
    message.feedback = {
      rating: type,
    }
  })
}

// 组件卸载时清理
onUnmounted(() => {
  if (audioPlayer.value) {
    audioPlayer.value.pause()
    audioPlayer.value.src = ''
  }
})
</script>

<template>
  <div>
    <div class="message message-user">
      <div class="message-avatar">
        <ElAvatar :icon="UserFilled" :size="40" class="user" />
      </div>
      <div class="message-content">
        <!-- <div class="message-role">
          您
        </div> -->

        <!-- 消息文本内容 - 最终回答 -->
        <div
          class="message-bubble  justify-end"
          :class="{ 'with-thoughts': hasThoughts }"
        >
          <!-- 文件内容（如图片）显示区域 -->
          <div
            v-if="message?.message_files && message?.message_files.length > 0"
            class="message-files"
          >
            <div v-for="file in message.message_files" :key="file.id" class="file-item">
              <img
                v-if="file.type === 'image'"
                :src="file.url"
                :alt="file.id"
                class="image-file w-[68px] h-[68px] shadow-md"
              ></img>
              <div v-else class="file-placeholder flex flex-wrap gap-2 mb-2">
                <div
                  class="group/file-item relative h-[68px] w-[144px] rounded-lg border-[0.5px] border-components-panel-border bg-components-card-bg p-2 shadow-xs hover:bg-components-card-bg-alt"
                >
                  <div
                    class="system-xs-medium mb-1 line-clamp-2 h-8 cursor-pointer break-all text-text-tertiary"
                    :title="file?.filename"
                  >
                    {{ file?.filename }}
                  </div>
                  <div class="relative flex items-center system-2xs-medium">
                    {{ file?.filename.split(".").pop().toUpperCase() }}
                    <div class="mx-1">
                      ·
                    </div>
                    {{ formatFileSize(file?.size || 0) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <template v-if="message.query">
            <div class="message-text" v-html="safedContent(message.query)" />
            <!-- 添加语音播放按钮 -->
            <div v-if="message.role === 'assistant'" class="message-actions">
              <ElButton
                v-if="!isPlaying"
                type="primary"
                :icon="Microphone"
                circle
                size="small"
                :loading="isLoading"
                @click="playAudio"
              />
              <ElButton
                v-else
                type="danger"
                :icon="VideoPause"
                circle
                size="small"
                @click="stopAudio"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="message message-assistant">
      <div class="message-avatar">
        <ElAvatar :icon="ChatRound" :size="40" class="assistant" />
      </div>
      <div class="message-content">
        <!-- <div class="message-role">
          AI助手
        </div> -->

        <!-- 思考过程面板 - 可折叠 -->
        <div v-if="hasThoughts" class="thought-panel">
          <!-- <div class="thought-header" @click="toggleThoughts">
            <ElIcon :class="{ 'is-rotate': showThoughts }">
              <ArrowRight />
            </ElIcon>
            <span>思考过程</span>
          </div> -->

          <div v-show="showThoughts" class="thought-content-panel">
            <!-- Agent思考过程显示区域 -->
            <div
              v-if="message.agent_thoughts && message.agent_thoughts.length > 0"
              class="agent-thoughts"
            >
              <div
                v-for="thought in sortedThoughts"
                :key="thought.id"
                class="thought-item"
              >
                <div v-if="thought.tool" class="thought-tool">
                  <div class="tool-name">
                    <ElIcon><SetUp /></ElIcon>
                    {{ thought.tool }}
                  </div>
                  <div v-if="thought.tool_input" class="tool-input">
                    {{ formatToolInput(thought.tool_input) }}
                  </div>
                </div>
                <div v-if="thought.thought" class="thought-content">
                  <!-- <div class="thought-label">
                    思考:
                  </div> -->
                  <div class="thought-text" v-html="safedContent(thought.thought)" />
                </div>
                <div v-if="thought.observation" class="thought-observation">
                  <!-- <div class="observation-label">
                    观察:
                  </div> -->
                  <div class="observation-text">
                    {{ thought.observation }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件内容（如图片）显示区域 -->
        <!-- <div
          v-if="message.message_files && message.message_files.length > 0"
          class="message-files"
        >
          <div v-for="file in message.message_files" :key="file.id" class="file-item">
            <img
              v-if="file.type === 'image'"
              :src="file.url"
              :alt="file.id"
              class="image-file"
            >
            <div v-else class="file-placeholder">
              {{ file.type }} 文件
            </div>
          </div>
        </div> -->

        <!-- 消息文本内容 - 最终回答 -->
        <div
          v-if="message.answer"
          class="message-bubble"
          :class="{ 'with-thoughts': hasThoughts }"
        >
          <div class="message-text" v-html="safedContent(message.answer || '暂无回答')" />
          <div v-if="message?.retriever_resources.length">
            <el-divider content-position="left">
              引用
            </el-divider>
            <div class="relative flex flex-wrap">
              <div v-for="item in message.retriever_resources" :key="item.document_id" class="mb-1 mr-1 cursor-pointer">
                <div class="flex h-7 max-w-[240px] items-center resources-item rounded-lg  px-2">
                  <ElTooltip placement="top" effect="light">
                    <template #content>
                      <div class="max-w-[680px] max-h-[380px] overflow-auto">
                        <div v-html="safedContent(item.content)" />
                      </div>
                    </template>
                    {{ item.document_name }}
                  </ElTooltip>
                </div>
              </div>
            </div>
          </div>
          <!-- 添加语音播放按钮 -->
          <!-- <div class="message-actions">
            <ElButton
              v-if="!isPlaying"
              type="primary"
              :icon="Microphone"
              circle
              size="small"
              :loading="isLoading"
              @click="playAudio"
            />
            <ElButton
              v-else
              type="danger"
              :icon="VideoPause"
              circle
              size="small"
              @click="stopAudio"
            />
          </div> -->
        </div>
        <div v-else class="message-text empty-content">
          空消息
        </div>
      </div>
      <div class="mes-option flex">
        <div class="flex">
          <span class="icon-bg">
            <span class="icon-item">
              <ElIcon title="复制" @click="copyMessage(message)">
                <DocumentCopy />
              </ElIcon>
            </span>
          </span>
          <span class="icon-bg">
            <span class="icon-item">
              <ElIcon title="刷新" @click="refreshMessage(message)">
                <RefreshLeft />
              </ElIcon>
            </span>
          </span>
          <span v-if="message?.feedback?.rating !== 'like' && message?.feedback?.rating !== 'dislike'" class="icon-bg" @click="feedbacksFuc(message, 'like')">
            <span class="icon-item">
              <i class="icon-good iconfont" title="赞" />
            </span>
          </span>
          <span v-if="message?.feedback?.rating === 'like'" class="icon-bg icon-like-active" @click="feedbacksFuc(message, null)">
            <span class="icon-item">
              <i class="icon-good iconfont" title="赞" />
            </span>
          </span>
          <span v-if="message?.feedback?.rating !== 'dislike' && message?.feedback?.rating !== 'like'" class="icon-bg" @click="feedbacksFuc(message, 'dislike')">
            <span class="icon-item">
              <i class="icon-diancai iconfont" title="踩" />
            </span>
          </span>
          <span v-if="message?.feedback?.rating === 'dislike'" class="icon-bg icon-dislike-active" @click="feedbacksFuc(message, null)">
            <span class="icon-item">
              <i class="icon-diancai iconfont" title="踩" />
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.message {
  display: flex;
  margin-bottom: 24px;
  animation: fade-in 0.3s ease-in-out;
  position: relative;
  width: 100%;
  .system-xs-medium {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    color: #676f83;
  }
  .system-2xs-medium {
    font-size: 10px;
    font-weight: 500;
    line-height: 12px;
    color: #676f83;
  }
}
.mes-option{
  position: absolute;
  // background: #fff;
  border-radius: 0.5rem;
  // box-shadow: 0 2px 3px 1px rgba(0, 0, 0, 0.08);
  padding: 5px 10px 5px 10px;
  bottom: -36px;
  left: 45px;
  cursor: pointer;
  .el-icon{
    font-size: 1.1rem;

  }

  .icon-bg{
     .icon-item{
      font-size: 1rem;
      padding: 4px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 34px;
      i{
        font-size: 1.1rem;
      }
     }
     &:hover{
      .icon-item{
        background: #eee;
        font-weight: bold;
        color: #666;
        border-radius: 0.375rem;
      // padding: 4px;
      }
    }
  }
  .icon-like-active{
    .icon-item{
      color: #283ddb;
      font-weight: bold;
    }
    &:hover{
      .icon-item{
        color: #283ddb;
        background: #155aef14;
        font-weight: bold;
      }
    }
  }
  .icon-dislike-active{
   .icon-item{
      color: #d92d20;
      font-weight: bold;
    }
    &:hover{
     .icon-item{
        color: #d92d20;
        background: #fef3f2;
        font-weight: bold;
      }
    }
  }
}
.text-text-tertiary,.resources-item {
    color: #676f83;
    font-size: 12px;
  }

.message-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.message-avatar :deep(.el-avatar) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.message-avatar :deep(.user) {
  background-color: #283ddb;
}

.message-avatar :deep(.assistant) {
  background-color: #10b981;
}

.message-content {
  /* flex: 1; */
  max-width: calc(100% - 52px);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-role {
  font-size: 0.85rem;
  font-weight: 600;
  color: #52525b;
}

/* 思考面板样式 */
.thought-panel {
  /* margin-bottom: 10px; */
  /* border: 1px solid #e4e4e7; */
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.thought-header {
  padding: 8px 12px;
  background-color: #f4f4f5;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #52525b;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.thought-header:hover {
  background-color: #e4e4e7;
}

.thought-header .el-icon {
  margin-right: 6px;
  transition: transform 0.3s;
  color: #71717a;
}

.thought-header .is-rotate {
  transform: rotate(90deg);
}

.thought-content-panel {
  padding: 12px;
  background-color: white;
}

/* Agent思考样式 */
.agent-thoughts {
  font-size: 0.9rem;
}

.thought-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #e4e4e7;
}

.thought-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.thought-tool {
  background-color: #f5f3ff;
  border-radius: 0.375rem;
  padding: 10px 12px;
  margin-bottom: 10px;
  border-left: 3px solid #155aef;
}

.tool-name {
  font-weight: 600;
  color: #155aef;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.tool-name .el-icon {
  margin-right: 6px;
}

.tool-input {
  font-family: "Fira Code", "SFMono-Regular", Consolas, Menlo, monospace;
  white-space: pre-wrap;
  background-color: #f4f4f5;
  padding: 10px;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  overflow-x: auto;
  color: #3f3f46;
}

.thought-content {
  background-color: #f4f4f5;
  border-radius: 0.375rem;
  padding: 10px 12px;
  margin-bottom: 10px;
}

.thought-label,
.observation-label {
  font-weight: 600;
  color: #52525b;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.thought-text {
  color: #27272a;
}

.thought-observation {
  background-color: #d1fae5;
  border-radius: 0.375rem;
  padding: 10px 12px;
  border-left: 3px solid #10b981;
}

.observation-text {
  color: #27272a;
}

/* 文件显示样式 */
.message-files {
  margin-bottom: 10px;
}

.file-item {
  margin-bottom: 8px;
}

.image-file {
  max-width: 100%;
  max-height: 300px;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.file-placeholder {
  // padding: 10px;
  background-color: #fff;
  border-radius: 0.5rem;
  color: #52525b;
}

/* 消息气泡 */
.message-bubble {
  padding: 14px 16px;
  border-radius: 0.5rem;
  position: relative;
  word-break: break-word;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.7);
  /* border: 1px solid #e4e4e7; */
}
.resources-item{
  border: 1px solid #f2f4f8;
  background: #fff;
  &:hover{
    background-color: #f2f4f8;
  }
}

/* .message-bubble.with-thoughts {
  border-left: 3px solid #155aef;
} */

.message-time {
  font-size: 0.75rem;
  color: #71717a;
  margin-left: 2px;
}

.message-text {
  line-height: 1.6;
  font-size: 15px;
  color: #27272a;
}

.empty-content {
  color: #71717a;
  font-style: italic;
}

.message-text :deep(p) {
  margin: 0 0 12px;
}

.message-text :deep(p:last-child) {
  margin-bottom: 0;
}

.message-text :deep(a) {
  color: #155aef;
  text-decoration: none;
}

.message-text :deep(pre) {
  background-color: #f4f4f5;
  border-radius: 0.375rem;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e4e4e7;
}

.message-text :deep(code) {
  font-family: "Fira Code", "SFMono-Regular", Consolas, Menlo, monospace;
  background-color: #f4f4f5;
  padding: 2px 4px;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

/* 当内容更新时添加高亮效果 */
.content-changed .message-bubble {
  animation: highlight 1s ease-in-out;
}

@keyframes highlight {
  0%,
  100% {
    background-color: white;
  }
  50% {
    background-color: #f5f3ff;
  }
}

/* User message */
.message-user {
  flex-direction: row-reverse;
}

.message-user .message-avatar {
  margin-right: 0;
  margin-left: 12px;
}

.message-user .message-role {
  text-align: right;
}

.message-user .message-bubble {
  background-color: #e1effe;
  /* border: 1px solid #a78bfa; */
  color: #27272a;
}

.message-user .message-time {
  text-align: right;
}

/* Typing animation */
.message-typing {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 24px;
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #a78bfa;
  margin: 0 3px;
  animation: typing 1.2s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.message-actions .el-button {
  padding: 6px;
}

.message-actions .el-button :deep(.el-icon) {
  font-size: 16px;
}
</style>
