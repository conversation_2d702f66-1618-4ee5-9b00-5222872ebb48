<script lang="ts" setup>
import type {
  DataTableColumns,
  DrawerPlacement,
  UploadCustomRequestOptions,
  UploadFileInfo,
} from 'naive-ui'
import {
  NButton,
  NButtonGroup,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NModal,
  NP,
  NText,
  NUpload,
  NUploadDragger,
  useMessage,
} from 'naive-ui'
import { computed, h, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { delDoc, getDatasetsInfo, getDocList } from '@/api/datasets'
import { useUserStore } from '@/store'
const message = useMessage()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const datasetsId = ref('')
const userInfo = computed(() => userStore.userInfo)
const userRoles = computed(() => userStore.roles)

const datasetsInfo = ref({})
const docList = ref([])
const pagination = ref({
  pageSize: 10,
})
interface RowData {
  name: string
  age: number
  address: string
  key: number
}
const columns: DataTableColumns<RowData> = [
  {
    title: '文档名称',
    key: 'name',
  },
  //   {
  //     title: '状态',
  //     key: 'enabled',
  //   },
  {
    title: '操作',
    key: 'options',
    render(row) {
      return h(
        NButton,
        {
          strong: false,
          tertiary: true,
          size: 'small',
          onClick: () => handleDel(row),
        },
        { default: () => '删除' },
      )
    },
  },
]
const active = ref(false)
const showModal = ref(false)
const delRow = ref({})
let renamedFile
const placement = ref<DrawerPlacement>('right')
const activate = (place: DrawerPlacement) => {
  active.value = true
  placement.value = place
}
const getData = async (id: string) => {
  const params = {
    limit: 99,
    page: 1,
    user: userInfo.value.userId,
    keyword: userRoles.value.includes('superadmin') ? '' : userInfo.value.userName,
  }
  console.log('userRoles', userRoles.value.includes('superadmin'))
  const data = await getDatasetsInfo(id || datasetsId.value, params)
  datasetsInfo.value = data
  const list = await getDocList(id || datasetsId.value, params)
  docList.value = list.data
}
const handleDel = async (row: any) => {
  delRow.value = row
  showModal.value = true
}
const cancelCallback = () => {
  showModal.value = false
}
const submitCallback = async () => {
  const data = await delDoc(datasetsId.value, delRow.value.id)
  getData(datasetsId.value)
}
// 允许的文件类型映射
const allowedFileTypes = {
  // 文本类型
  '.txt': 'text/plain',
  '.md': 'text/markdown',
  '.markdown': 'text/markdown',
  '.mdx': 'text/markdown',
  '.html': 'text/html',
  '.htm': 'text/html',
  '.csv': 'text/csv',
  '.vtt': 'text/vtt',
  '.properties': 'text/plain',

  // PDF
  '.pdf': 'application/pdf',

  // Office 文档
  '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  '.xls': 'application/vnd.ms-excel',
  '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
}
// 获取文件扩展名
const getFileExtension = (filename: string): string => {
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1)
    return ''
  return filename.slice(lastDotIndex).toLowerCase()
}
// 文件上传前的验证
// 文件上传前的验证
const beforeUpload = (data: {
  file: UploadFileInfo
  fileList: UploadFileInfo[]
}): boolean | Promise<boolean> => {
  const { file } = data
  // console.log(file)
  // debugger
  // 检查文件是否存在
  if (!file.file) {
    message.error('无效的文件')
    return false
  }
  const newName = `${userInfo.value.userName}-${file.name}`
  renamedFile = new File([file.file], newName, {
    type: file.file.type,
    lastModified: file.file.lastModified,
    size: file.file.size,
  })
  // 获取文件扩展名
  const fileExtension = getFileExtension(file.name)

  // 验证文件类型
  if (!Object.keys(allowedFileTypes).includes(fileExtension)) {
    message.error(`不支持的文件类型: ${fileExtension || '未知'}`)
    return false
  }

  // 验证MIME类型
  const expectedMime = allowedFileTypes[fileExtension as keyof typeof allowedFileTypes]
  if (file.file.type && file.file.type !== expectedMime) {
    message.error(`文件类型不匹配: 应为 ${expectedMime}，实际为 ${file.file.type}`)
    return false
  }

  // 文件大小验证 (15MB)
  const maxSize = 15 * 1024 * 1024
  if (file.file.size > maxSize) {
    message.error('文件大小不能超过15MB')
    return false
  }

  return true
}
const customRequest = async ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  try {
    // 准备FormData
    const formData = new FormData()
    // 添加文件
    if (renamedFile || file.file)
      formData.append('file', renamedFile || file.file)

    // 添加额外数据（使用JSON.stringify）
    const params = {
      indexing_technique: datasetsInfo.value.indexing_technique || 'economy',
      doc_form: datasetsInfo.value.doc_form || 'text_model',
      doc_language: 'Chinese',
      process_rule: {
        mode: 'automatic',
        rules: null,
      },
      retrieval_model: {
        search_method: datasetsInfo.value.retrieval_model_dict.search_method,
        reranking_enable: datasetsInfo.value.retrieval_model_dict.reranking_enable,
        reranking_model: datasetsInfo.value.retrieval_model_dict.reranking_model,
        top_k: datasetsInfo.value.retrieval_model_dict.top_k,
        score_threshold_enabled:
          datasetsInfo.value.retrieval_model_dict.score_threshold_enabled,
        score_threshold: datasetsInfo.value.retrieval_model_dict.score_threshold,
      },
      embedding_model: datasetsInfo.value.embedding_model,
      embedding_model_provider: datasetsInfo.value.embedding_model_provider,
    }
    formData.append('data', JSON.stringify(params))
    const url = `${import.meta.env.VITE_AI_API_BASE}/datasets/${
      datasetsId.value
    }/document/create-by-file`
    // 执行上传请求
    await axios.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${import.meta.env.VITE_DATASETS_API_KEY}`, // 添加认证头
      },
      // 可选：如果需要进度回调可以保留
      //   onUploadProgress: (progressEvent: AxiosProgressEvent) => {
      //     // 这里可以处理进度，但根据要求不显示
      //   },
    })

    // 通知 Naive UI 上传完成
    onFinish()
    message.success(`${file.name} 上传成功`)
  }
  catch (error: any) {
    // 处理错误
    const errorMsg = error.response?.data?.message || error.message || '未知错误'

    // 通知 Naive UI 上传出错
    onError()
    console.error(`${file.name} 上传失败: ${errorMsg}`)
  }
}

// 处理上传完成（所有文件）
const handleFinish = (file: UploadFileInfo) => {
  getData(datasetsId.value)
}

// 处理上传错误
const handleError = (options: { file: UploadFileInfo; event?: ProgressEvent }) => {
  console.error('上传出错:', options.file, options.event)
}
onMounted(() => {
  datasetsId.value = route.query.id || ''
  datasetsId.value && getData(datasetsId.value)
})
</script>

<template>
  <div v-if="datasetsId" class="datasets-button-wrapper">
    <NButtonGroup>
      <NButton strong type="info" @click="activate('right')">
        <template #icon>
          <i class="iconfont icon-beianbiao" />
        </template>
        知识库
      </NButton>
    </NButtonGroup>
  </div>

  <NDrawer v-model:show="active" :width="502" :placement="placement">
    <NDrawerContent title="知识库">
      <div class="upload-area">
        <NUpload
          directory-dnd
          :custom-request="customRequest"
          :show-file-list="false"
          @finish="handleFinish"
          @error="handleError"
          @before-upload="beforeUpload"
        >
          <NUploadDragger>
            <div style="margin-bottom: 12px">
              <NButton strong type="info">
                上传文件
              </NButton>
            </div>
            <NText style="font-size: 16px">
              点击或者拖动文件到该区域来上传
            </NText>
            <NP depth="3" style="margin: 8px 0 0 0; font-size: 12px">
              支持
              TXT、MARKDOWN、MDX、PDF、HTML、XLSX、XLS、DOCX、CSV、VTT、PROPERTIES、MD、HTM，每个文件不超过
              15MB
            </NP>
          </NUploadDragger>
        </NUpload>
      </div>
      <div class="datasets-info">
        <NDataTable :columns="columns" :data="docList" :pagination="pagination" />
      </div>
    </NDrawerContent>
  </NDrawer>
  <NModal
    v-model:show="showModal"
    preset="dialog"
    title="确认"
    :content="`确认删除该知识库文档《${delRow.name}》吗？`"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submitCallback"
    @negative-click="cancelCallback"
  />
</template>

<style lang="less" scoped>
.datasets-button-wrapper {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 9;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
</style>
