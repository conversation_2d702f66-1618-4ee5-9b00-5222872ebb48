<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { NLayout, NLayoutContent, useMessage } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import Permission from './Permission.vue'
import Sider from './sider/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import {
  gptConfigStore,
  homeStore,
  useAppStore,
  useAuthStore,
  useChatStore,
} from '@/store'
import { aiFooter, aiHeader, aiSider } from '@/views/mj'
import { t } from '@/locales'
import { openaiSetting } from '@/api'
import { isObject } from '@/utils/is'
import { getToken } from '@/store/modules/auth/helper'

const router = useRouter()
const appStore = useAppStore()
const chatStore = useChatStore()
const authStore = useAuthStore()

const rt = useRoute()
const ms = useMessage()
openaiSetting(rt.query)
if (rt.name == 'GPTs') {
  const model = `gpt-4-gizmo-${rt.params.gid.toString()}`
  gptConfigStore.setMyData({ model })
  ms.success(`GPTs ${t('mj.modleSuccess')}`)
}
else if (rt.name == 'Setting') {
  openaiSetting(rt.query)
  if (isObject(rt.query))
    ms.success(t('mj.setingSuccess'))
}
else if (rt.name == 'Model') {
  const model = `${rt.params.gid.toString()}`
  gptConfigStore.setMyData({ model })
  ms.success(t('mj.modleSuccess'))
}

// router.replace({ name: 'dev', params: { type: 'k', code: 't53nctLjuOiKDtrX' } })
homeStore.setMyData({ local: 'Chat' })
const { isMobile } = useBasicLayout()

const collapsed = computed(() => appStore.siderCollapsed)

const needPermission = computed(() => {
  // mlog( 'Layout token',  authStore.token   )

  return !!authStore.session?.auth && !authStore.token
})

const getMobileClass = computed(() => {
  if (isMobile.value)
    return ['rounded-none', 'shadow-none']
  return ['shadow-md', 'dark:border-neutral-800'] // 'border', 'rounded-md',
})

const getContainerClass = computed(() => {
  return ['', { abc: !isMobile.value && !collapsed.value }]
})
onMounted(() => {
  if (!getToken())
    router.push('/login')
})
</script>

<template>
  <div
    class="dark:bg-[#24272e] transition-all p-0 111"
    :class="[isMobile ? 'h55' : 'h-full']"
  >
    <div class="h-full overflow-hidden" :class="getMobileClass">
      <aiHeader />
      <NLayout class="z-40 transitio h55" has-sider>
        <aiSider />
        <Sider />
        <NLayoutContent class="h-full">
          <RouterView v-slot="{ Component, route }">
            <component :is="Component" :key="route.fullPath" />
          </RouterView>
        </NLayoutContent>
      </NLayout>
    </div>
    <Permission :visible="needPermission" />
  </div>
  <!-- <aiMobileMenu v-if="isMobile" /> -->

  <aiFooter />
</template>

<style>
.h55 {
  height: calc(100% - 56px);
}
</style>
