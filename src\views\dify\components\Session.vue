<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, NLayoutSider } from 'naive-ui'
import { ElIcon, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

import { computed, onMounted, ref, watch } from 'vue'
import { Delete, Edit, MoreFilled } from '@element-plus/icons-vue'
import { chatApi, renameSession } from '../../../api/difychat'
import { useChatStore } from './../../../store/chat'
import { useAppStore, useAuthStore, useUserStore } from '@/store'
import { delSession } from '@/api/difychat'
// 导入实际的 chatApi 对象

const router = useRouter()
const appStore = useAppStore()
const chatStore = useChatStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const appInfo = computed(() => chatStore.appInfo)
const isLoading = computed(() => chatStore.isLoading)
const chatComplete = computed(() => chatStore.chatComplete)

const sessionList = ref([])
const currentSession = ref({})
const currentSessionId = computed(() => currentSession.value?.id)
const getData = () => {
  const param = {
    user: userInfo.value?.userId,
    last_id: '',
    limit: 20,
  }
  chatApi.getSessionList(param).then((res) => {
    sessionList.value = res.data.data
    if (sessionList.value.length > 0 && !currentSessionId.value)
      currentSession.value = sessionList.value[0]
    chatStore.saveCurrentSession(sessionList.value[0])
  })
}
const handleSession = (item) => {
  currentSession.value = item
  chatStore.saveCurrentSession(item)
}
const renameSessionFuc = (item) => {
  ElMessageBox.prompt('会话名称', '重命名会话', {
    confirmButtonText: '保存',
    cancelButtonText: '取消',
    // inputPattern: /^\S+$/,
    // inputErrorMessage: '会话名称不能为空',
    inputValue: item.name,
  })
    .then(({ value }) => {
      const param = {
        user: userInfo.value?.userId,
        name: value,
        auto_generate: false,
      }
      renameSession(item.id, param).then((res) => {
        getData()
      })
    })
    .catch(() => {

    })
}
const delSessionFuc = (item: any) => {
  ElMessageBox.confirm('您确定要删除此对话吗？', '删除对话', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const param = {
      user: userInfo.value?.userId,
    }
    delSession(item.id, param).then((res) => {
      sessionList.value = sessionList.value.filter(i => i.id !== item.id)
      getData()
    })
  }).catch(() => {
    console.log('取消删除')
  })
}
const newAddSession = () => {
  const isHaveNew = sessionList.value.some(item => item.name === '新对话')
  if (isHaveNew)
    return
  if (!currentSession.value.id)
    return
  const param = {
    name: '新对话',
    id: '',
    status: 'normal',
  }
  currentSession.value = param
  chatStore.saveCurrentSession(param)
  sessionList.value.unshift(param)
  // newSession(param).then((res) => {
  //   getData()
  // })
}

// 监听 store 中的 chatComplete 值
watch(chatComplete, (newVal) => {
  if (newVal && currentSession.value.id === '')
    getData()
})

onMounted(() => {
  getData()
})
</script>

<template>
  <NLayoutSider
    collapse-mode="transform"
    :collapsed-width="0"
    :width="240"
    show-trigger="arrow-circle"
    content-style="padding: 24px;background-color: #e9ebf1;"
    bordered
  >
    <div class="font-bold truncate overflow-hidden ...">
      <span :title="appInfo?.name">{{ appInfo?.name }}</span>
    </div>
    <div class="new-session" @click="newAddSession">
      <NButton color="#fff">
        开启新对话
      </NButton>
    </div>
    <div class="session-list mt-5">
      <div
        v-for="item in sessionList"
        :key="item.id"
        class="session-item flex justify-between items-center"
        :class="{ active: item.id === currentSessionId }"
      >
        <div
          class="session-name grow truncate py-2 px-2"
          :title="item.name"
          @click="handleSession(item)"
        >
          {{ item.name }}
        </div>
        <div v-if="item.id" class="session-opt pr-2 items-center justify-start">
          <ElTooltip placement="top" effect="light">
            <template #content>
              <div class="max-w-[120px] overflow-auto text-left">
                <el-button text :icon="Edit" @click="renameSessionFuc(item)">
                  重命名
                </el-button>
                <br>
                <el-button text type="danger" :icon="Delete" @click="delSessionFuc(item)">
                  删&emsp;除
                </el-button>
              </div>
            </template>
            <ElIcon><MoreFilled /></ElIcon>
          </ElTooltip>
        </div>
      </div>
    </div>
  </NLayoutSider>
</template>

<style lang="less">
.new-session {
  margin-top: 20px;
  .n-button {
    width: 100%;
    color: #283ddb;
    font-size: 13px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    .n-button__content {
      font-weight: bold;
    }
    &:hover,&:active,&:focus{
      background: rgba(254, 253, 253, 0.6) !important;
      color: #283ddb !important;
    }
  }
}
.session-item {
  cursor: pointer;
  border-radius: 4px;
  &.active {
    color: #283ddb;
    background-color: #155aef14;
  }
  &:hover {
    background-color: #e2e2e2;
  }
  .session-name {
    padding-right: 35px;
  }
}
</style>
