<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
// import { ChatDotRound, Delete } from '@element-plus/icons-vue'
import { marked } from 'marked'
import { chatApi } from '../../api/difychat'
import { useChatStore } from './../../store/chat'
import MessageInput from './components/MessageInput.vue'
import MessageItem from './components/MessageItem.vue'
import MessageList from './components/MessageList.vue'
import { useUserStore } from '@/store'

const chatStore = useChatStore()
const userStore = useUserStore()
const chatMessages = computed(() => chatStore.messages)
const isLoading = computed(() => chatStore.isLoading)
const currentSession = computed(() => chatStore.currentSession)
const appInfo = computed(() => chatStore.appInfo)
const appParams = computed(() => chatStore.appParams)
const appMeta = computed(() => chatStore.appMeta)
const error = computed(() => chatStore.error)

const userInfo = computed(() => userStore.userInfo)
const chatContainerRef = ref<HTMLElement | null>(null)
const historyMes = ref([])

const messageInputRef = ref<InstanceType<typeof MessageInput> | null>(null)

const hasMessages = computed(() => chatMessages.value.length > 0)
const hasUserInput = computed(() => chatMessages.value.some(m => m.role === 'user'))
const hasAssistantMessage = computed(() =>
  chatMessages.value.some(m => m.role === 'assistant'),
)
const stopTask = ref(false)

const scrollToBottom = async () => {
  await nextTick()
  if (chatContainerRef.value) {
    const container = chatContainerRef.value
    container.scrollTop = container.scrollHeight
  }
}

const handleSendMessage = async (message: string, uploadFile: any) => {
  console.log('uploadFile === ', uploadFile)
  if (!message.trim())
    return

  try {
    await chatStore.sendMessage(message, uploadFile)
    await scrollToBottom()
  }
  catch (err) {
    console.error('发送消息失败:', err)
  }
}

const handleClearChat = () => {
  chatStore.clearMessages()
}
// 定义一个函数来改变 MessageInput 组件的 stopTasks 属性值

const changeStopTaskFuc = (val: boolean) => {
  stopTask.value = val
}
const refreshSendMessage = (message: string, uploadFile: any) => {
  handleSendMessage(message, uploadFile)
  changeStopTaskFuc(true)
}

const useExamplePrompt = (prompt: string) => {
  handleSendMessage(prompt, [])
}
// 获取应用信息和参数
const getInfo = () => {
  chatApi.getAppInfo().then((res) => {
    chatStore.saveAppInfo(res.data)
  })
  chatApi.getAppParams().then((res) => {
    chatStore.saveAppParams(res.data)
  })
  chatApi.getAppMeta().then((res) => {
    chatStore.saveAppMeta(res.data)
  })
}
// 获取聊天历史记录
const getHistoryMessage = () => {
  const param = {
    user: userInfo.value?.userId,
    first_id: '',
    limit: 20,
    conversation_id: currentSession.value?.id,
  }
  chatApi.getSessionMessages(param).then((res) => {
    // 将获取到的聊天历史数据与现有消息合并
    // const newMessages = res.data || []
    // console.log(res.data.data)
    historyMes.value = res.data.data || []
    // chatStore.setMessages(res?.data.data)
    scrollToBottom()
  })
}
// 安全渲染内容
const safedContent = (content: any) => {
  if (!content)
    return ''

  try {
    // 尝试使用marked处理Markdown
    return marked(content, { breaks: true })
  }
  catch (err) {
    console.error('Markdown解析错误:', err)

    // 如果解析失败，返回简单的HTML转义版本
    return escapeHtml(content)
  }
}
// HTML转义函数
function escapeHtml(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .replace(/\n/g, '<br>')
}
// 监听 currentSession 的值从无到有，或者新值不等于旧值
watch(
  currentSession,
  (newVal, oldVal) => {
    if ((!oldVal && newVal) || (newVal && oldVal && newVal.id !== oldVal.id))
      getHistoryMessage()
    chatStore.clearMessages() // 清空现有消息，准备加载新的聊天历史数据
  },
  { immediate: false },
)
onMounted(() => {
  // 加载页面时滚动到底部
  getInfo()
  scrollToBottom()
})

// 监听消息列表变化，自动滚动到底部
watch(
  chatMessages,
  () => {
    scrollToBottom()
  },
  { deep: true },
)
</script>

<template>
  <div class="chat-container dify-chat-wrapper">
    <!-- <div class="dify-chat-header">
      <div class="header-content">
        <div class="logo-area">
          <div class="logo" />
          <h1 class="app-title">
            Dify AI-Web
          </h1>
        </div>
        <el-button
          v-if="hasMessages"
          class="clear-button"
          size="small"
          @click="handleClearChat"
        >
          <!-- <el-icon><Delete /></el-icon> -->
    <!-- <span>清除对话</span>
        </el-button>
      </div>
    </div> -->

    <div ref="chatContainerRef" class="messages-container">
      <!-- Empty state v-if="!hasMessages && historyMes.length === 0" -->
      <div class="empty-state">
        <div class="welcome-message">
          <h2>欢迎使用 Dify AI 助手</h2>
          <!-- <p>我可以帮助您回答问题、提供信息或者进行轻松对话。</p> -->
        </div>

        <div class="examples-container">
          <div
            class="message-text"
            v-html="safedContent(appParams?.opening_statement || '')"
          />
        </div>
        <div
          v-if="appParams?.suggested_questions.length > 0"
          class="suggested-questions flex flex-wrap"
        >
          <div
            v-for="(item, index) in appParams?.suggested_questions"
            :key="index"
            class="system-sm-medium mr-1 mt-1 inline-flex max-w-full shrink-0 cursor-pointer flex-wrap rounded-lg border-[0.5px] px-2 py-1 shadow-xs last:mr-0"
            @click="useExamplePrompt(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <div v-if="historyMes.length > 0">
        <MessageList
          v-for="(message, index) in historyMes"
          :key="index"
          :message="message"
          :stop-task="stopTask"
          @send="refreshSendMessage"
        />
      </div>
      <!-- Chat messages -->
      <template v-if="chatMessages.length > 0">
        <MessageItem
          v-for="(message, index) in chatMessages"
          :key="index"
          :message="message"
          :stop-task="stopTask"
          @send="refreshSendMessage"
        />
      </template>

      <!-- Loading indicator -->
      <div v-if="isLoading && hasUserInput" class="loading-container">
        <div class="loading-indicator">
          <span>正在处理您的请求...</span>
        </div>
      </div>

      <!-- Error message -->
      <div v-if="error" class="error-message">
        <el-alert
          title="出错了"
          type="error"
          :description="error"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <div class="input-container">
      <MessageInput ref="messageInputRef" :disabled="isLoading" :stop-task="stopTask" @task="changeStopTaskFuc" @send="handleSendMessage" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.dify-chat-wrapper {
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    background-color: #fafafa;
  }

  .dify-chat-header {
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    z-index: 10;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
  }

  .logo-area {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-icon {
    width: 28px;
    height: 28px;
  }

  .app-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #27272a;
    margin: 0;
  }

  .clear-button {
    color: #52525b;
    background-color: transparent;
    border: 1px solid #d4d4d8;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .clear-button:hover {
    color: #ef4444;
    border-color: #ef4444;
    background-color: #fef2f2;
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-height: calc(100vh - 130px);
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    // text-align: center;
    padding: 0 1rem;
    margin-top: -4rem;
  }

  .welcome-message {
    margin-bottom: 2.5rem;
    max-width: 600px;
  }

  .welcome-message h2 {
    color: #283ddb;
    margin-bottom: 1rem;
    font-size: 1.75rem;
  }

  .welcome-message p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #52525b;
  }
  .message-text :deep(p) {
  margin: 0 0 12px;
}

  .examples-container,
  .suggested-questions {
    width: 100%;
    max-width: 700px;
  }

  .examples-container h3 {
    margin-bottom: 1rem;
    font-weight: 600;
    color: #3f3f46;
    font-size: 1.1rem;
  }
  .suggested-questions {
    .system-sm-medium {
      color: #155aef;
      &:hover {
        background: #f3f9ff;
      }
    }
    .shadow-xs {
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .example-prompts {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }

  .example-prompt {
    padding: 1rem;
    background-color: white;
    border: 1px solid #e4e4e7;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.5;
    color: #3f3f46;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .example-prompt .el-icon {
    color: #283ddb;
    margin-top: 0.125rem;
  }

  .example-prompt:hover {
    background-color: #f5f3ff;
    border-color: #00b9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .loading-container {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
  }

  .loading-indicator {
    padding: 0.5rem 1rem;
    background-color: white;
    border-radius: 2rem;
    color: #283ddb;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .error-message {
    margin: 1rem 0;
  }

  .input-container {
    padding: 1rem;
    // background-color: white;
    // border-top: 1px solid #e4e4e7;
    // box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
    position: absolute;
    bottom: 10px;
    width: 100%;
  }
}

/* 媒体查询适配不同屏幕尺寸 */
@media (max-width: 768px) {
  .chat-container {
    padding: 0;
  }

  .example-prompts {
    grid-template-columns: 1fr;
  }

  .messages-container {
    padding: 1rem 0.75rem;
  }
}
</style>
