import { defineStore } from 'pinia'
import type { TenantInfo, TenantState } from './helper'
import { defaultSetting, getLocalState, setLocalState } from './helper'

export const useTenantStore = defineStore('tenant-store', {
  state: (): TenantState => getLocalState(),
  
  getters: {
    getTenantLogo: (state) => state.tenantInfo?.tenantLogo || '',
    getHomeLogo: (state) => state.tenantInfo?.homeLogo || '',
    getMobileLogo: (state) => state.tenantInfo?.mobileLogo || '',
    getCompanyName: (state) => state.tenantInfo?.companyName || '',
  },
  
  actions: {
    updateTenantInfo(tenantInfo: TenantInfo) {
      this.tenantInfo = tenantInfo
      this.recordState()
    },

    resetTenantInfo() {
      this.tenantInfo = null
      this.recordState()
    },

    recordState() {
      setLocalState(this.$state)
    },
  },
})
