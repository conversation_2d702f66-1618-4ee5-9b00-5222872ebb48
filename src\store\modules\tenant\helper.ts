import { ss } from '@/utils/storage'

const LOCAL_NAME = 'tenantStorage'

export interface TenantInfo {
  id: string
  tenantId: string
  contactUserName: string | null
  contactPhone: string | null
  companyName: string
  licenseNumber: string | null
  address: string | null
  domain: string | null
  intro: string | null
  remark: string | null
  packageId: string | null
  expireTime: string
  accountCount: number | null
  status: string | null
  tenantLogo: string
  homeLogo: string
  mobileLogo: string
}

export interface TenantState {
  tenantInfo: TenantInfo | null
}

export function defaultSetting(): TenantState {
  return {
    tenantInfo: null,
  }
}

export function getLocalState(): TenantState {
  const localSetting: TenantState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: TenantState): void {
  ss.set(LOCAL_NAME, setting)
}
