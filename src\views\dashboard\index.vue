<!-- eslint-disable vue/no-parsing-error -->
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
const src = ref('')
const code = ref('')
const type = route.query.type
const aiList = [
  {
    name: '企业科创服务专家',
    icon: new URL('@/assets/img/icon-5.png', import.meta.url).href,
    url: '/agent/declare?type=k&code=c5oUfSOakFnS9brEe',
    code: 'c5oUfSOakFnS9brE',
    type: 'k',
  },
  {
    name: 'AI技术经纪人',
    icon: new URL('@/assets/img/icon-6.png', import.meta.url).href,
    url: '/agent/declare?type=k&code=2xNxaN7XA4IyjtCY',
    code: '2xNxaN7XA4IyjtCY',
    type: 'k',
  },
  {
    name: '智能客服',
    icon: new URL('@/assets/img/icon-6.png', import.meta.url).href,
    url:
      '/agent/employee?type=k&code=GvdZdiaY2zG7x9zu&id=d0759591-9db4-4797-9757-6c5fd070088a',
    code: 'GvdZdiaY2zG7x9zu',
    type: 'k',
  },
  {
    name: '企业政策智脑',
    icon: new URL('@/assets/img/icon-1.png', import.meta.url).href,
    url:
      '/agent/declare?type=k&code=NWL7LnV2gGy65pxw&id=40bfebfa-a675-41a2-8311-3734045e4be3',
    code: 'NWL7LnV2gGy65pxw',
    type: 'k',
    id: '40bfebfa-a675-41a2-8311-3734045e4be3',
  },
  {
    name: '项目立项智能助手',
    icon: new URL('@/assets/img/icon-2.png', import.meta.url).href,
    url: '/agent/dev?type=k&code=R8SlXI1v5Zt8KLNl',
    code: 'R8SlXI1v5Zt8KLNl',
    type: 'k',
  },
  {
    name: '企业研发活动智能评审专家',
    icon: new URL('@/assets/img/icon-3.png', import.meta.url).href,
    url: '/agent/dev?type=k&code=h0FDudxD4lkbSnVV',
    code: 'h0FDudxD4lkbSnVV',
    type: 'k',
  },
  {
    name: '高企智能测评',
    icon: new URL('@/assets/img/icon-4.png', import.meta.url).href,
    url: '/agent/declare?type=k&code=fJokI6IWsauydItf',
    code: 'fJokI6IWsauydItf',
    type: 'k',
  },
  {
    name: '创新型中小企业智能测评',
    icon: new URL('@/assets/img/icon-3.png', import.meta.url).href,
    url:
      '/agent/declare?type=k&code=0JVStUaU5axyO2x4&id=1fdb6d0a-7743-4310-8429-51e8ee765a99',
    code: '0JVStUaU5axyO2x4',
    type: 'k',
  },
  {
    name: '专精特新中小企业智能测评',
    icon: new URL('@/assets/img/icon-4.png', import.meta.url).href,
    url:
      '/agent/declare?type=k&code=FcNFJRlFTM3TJn5Q&id=2808b376-fa36-4e42-8000-c335beb64915',
    code: 'FcNFJRlFTM3TJn5Q',
    type: 'k',
  },
  {
    name: '专精特新小巨人智能测评',
    icon: new URL('@/assets/img/icon-5.png', import.meta.url).href,
    url:
      '/agent/declare?type=k&code=Or0EEfQhFR37olsW&id=44a4a02d-9948-4687-88c2-d8b8f960b3b7',
    code: 'Or0EEfQhFR37olsW',
    type: 'k',
  },
]
const appList = [
  {
    name: '政策申报',
    icon: new URL('@/assets/img/icon-11.png', import.meta.url).href,
    link: 'https://ai.fitkc.cn/policy',
    type: 'chain',
    id: 53,
  },
  {
    name: '知识产权',
    icon: new URL('@/assets/img/icon-11.png', import.meta.url).href,
    link: 'https://ai.fitkc.cn/high-tech-search',
    type: 'chain',
    id: 54,
  },
  {
    name: '科企高质量发展分析平台',
    icon: new URL('@/assets/img/icon-12.png', import.meta.url).href,
    link: 'https://tqap.fitkc.cn/',
    type: 'chain',
    id: 55,
  },
  {
    name: '科研云多政策口径研发数智软件',
    icon: new URL('@/assets/img/icon-13.png', import.meta.url).href,
    link: 'https://kyy.rndcloud.cn/',
    type: 'chain',
    id: 56,
  },
  {
    name: '科技成果转化产学研合作平台',
    icon: new URL('@/assets/img/icon-14.png', import.meta.url).href,
    link: 'https://ttc.fitkc.cn/',
    type: 'chain',
    id: 57,
  },
  {
    name: '创新学院',
    icon: new URL('@/assets/img/icon-15.png', import.meta.url).href,
    link: 'https://www.new.qxbyun.com/',
    type: 'chain',
    id: 58,
  },
  {
    name: '科品电商',
    icon: new URL('@/assets/img/icon-16.png', import.meta.url).href,
    link: 'https://h5.hnkph.cn/static/html/pc.html#/',
    type: 'chain',
    id: 59,
  },
]

const menuItemClick = (item) => {
  if (item.type === 'chain') {
    window.open(item.link, '_blank')
    return
  }
  if (item.code) {
    router.push({
      path: item.url,
      query: { code: item.code, type: item.type, origin: 'dashboard' },
    })
  }
}

onMounted(() => {
  // src.value = decodeURIComponent(
  //   route.query.href || 'https://k.fitkc.cn/agent/NWL7LnV2gGy65pxw',
  // )
})
</script>

<template>
  <div class="dashboard-wrapper" style="background-color: #eee">
    <div class="title">
      <h3>FIT飞地科服AI智脑·全域赋能</h3>
      <p>企业级全场景数智化决策中枢，私有化大模型+多角色AGENT驱动商业价值跃升。</p>
    </div>
    <div class="panel-box">
      <div class="panel-title">
        科企AI工具
      </div>
      <div class="grid grid-cols-5">
        <div
          v-for="(cell, index) in aiList"
          :key="index"
          class="item"
          :class="index != 4 || index != 8 ? 'borderR' : ''"
          @click="menuItemClick(cell)"
        >
          <img :src="cell.icon" alt="">
          <p class="mt-2">
            {{ cell.name }}
          </p>
        </div>
      </div>
    </div>

    <div class="panel-box mt-2 border-all">
      <div class="panel-title">
        科企数智平台
      </div>
      <div class="grid grid-cols-5">
        <div
          v-for="(cell, index) in appList"
          :key="index"
          class="item"
          :class="index != 4 || index != 8 ? 'borderR' : ''"
          @click="menuItemClick(cell)"
        >
          <img :src="cell.icon" alt="">
          <p class="mt-2">
            {{ cell.name }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.dashboard-wrapper {
  height: 100%;
  .title {
    width: 100%;
    height: 200px;
    background: url(@/assets/img/dashbord-bg.png) no-repeat right;
    background-size: cover;
    padding-left: 110px;
    h3 {
      font-weight: bold;
      font-size: 36px;
      color: #001bc0;
      position: relative;
      padding-top: 40px;
    }
    p {
      font-size: 16px;
      color: #000000;
    }
  }
  .panel-box {
    min-height: 318px;
    background: #ffffff;
    border-radius: 0 0 20px 20px;
    .panel-title {
      line-height: 56px;
      font-size: 18px;
      color: #000000;
      border-bottom: 1px solid #edf0ff;
      padding-left: 28px;
    }
    .item {
      text-align: center;
      height: 130px;
      padding-top: 30px;
      cursor: pointer;
      &:nth-child(-n + 5) {
        border-bottom: 1px solid #edf0ff;
      }
      img {
        margin: 0 auto;
      }
      transition: transform 0.3s ease;
      &:hover {
        img,
        p {
          transform: scale(1.1);
        }
      }
    }
    .borderR {
      border-right: 1px solid #edf0ff;
    }
    .borderB {
      border-bottom: 1px solid #edf0ff;
    }
  }
  .border-all {
    border-radius: 20px;
  }
}
</style>
