<script setup lang="ts">
import { NLayoutSider } from 'naive-ui'
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import '@/assets/img/fdqf-logo.png'
import '@/assets/img/kyy-logo.png'
import '@/assets/img/cgzh-logo.png'
import '@/assets/img/qxb-logo.png'
import '@/assets/img/kph-logo.png'
import '@/assets/img/tqap-logo.png'
const appStore = useAppStore()
const router = useRouter()
const route = useRoute()
const subMenuOptions = ref({
  dev: [
    {
      name: '项目立项书智能助手',
      icon: 'icon-zhengcegonggao',
      link: 'https://k.fitkc.cn/chat/t53nctLjuOiKDtrX',
      code: 't53nctLjuOiKDtrX',
      type: 'k',
      id: 1,
    },
    {
      name: '企业研发活动智能评审专家',
      icon: 'icon-icon-pingshenzhuanjia-xuanzhong',
      link: 'https://k.fitkc.cn/chat/h0FDudxD4lkbSnVV',
      code: 'h0FDudxD4lkbSnVV',
      type: 'k',
      id: 2,
      datasetsId: '98e6f54a-d1b2-4130-a93b-6cca804601af',
    },
    {
      name: '高企智能测评',
      icon: 'icon-zhinengceping',
      link: 'https://k.fitkc.cn/chat/fJokI6IWsauydItf',
      code: 'fJokI6IWsauydItf',
      type: 'k',
      id: 3,
    },
    {
      name: '企业科创服务专家',
      icon: 'icon-product',
      link: 'https://k.fitkc.cn/chat/c5oUfSOakFnS9brE',
      code: 'c5oUfSOakFnS9brE',
      type: 'k',
      id: 4,
    },
    {
      name: 'AI技术经理人',
      icon: 'icon-jingliren_o1',
      link: 'https://k.fitkc.cn/chat/6oRLP8gFgPCgvyK0',
      code: '6oRLP8gFgPCgvyK0',
      type: 'k',
      id: 5,
    },
    {
      name: '创新型中小企业智能测评',
      icon: 'icon-zhongxiaoqiye',
      link: 'https://k.fitkc.cn/chat/0JVStUaU5axyO2x4',
      code: '0JVStUaU5axyO2x4',
      type: 'k',
      id: 6,
      datasetsId: '1fdb6d0a-7743-4310-8429-51e8ee765a99',
    },
    {
      name: '专精特新中小企业智能测评',
      icon: 'icon-zhuanjingtexin',
      link: 'https://k.fitkc.cn/chat/FcNFJRlFTM3TJn5Q',
      code: 'FcNFJRlFTM3TJn5Q',
      type: 'k',
      id: 7,
      datasetsId: '2808b376-fa36-4e42-8000-c335beb64915',
    },
    {
      name: '专精特新小巨人智能测评',
      icon: 'icon-xiaojuren',
      link: 'https://k.fitkc.cn/chat/Or0EEfQhFR37olsW',
      code: 'Or0EEfQhFR37olsW',
      type: 'k',
      id: 8,
      datasetsId: '44a4a02d-9948-4687-88c2-d8b8f960b3b7',
    },
    {
      name: 'AI架构规划报告',
      icon: 'icon-icon_AI',
      link: 'https://agent.rxyai.com/chat/qmojnT3SUhteRFn4',
      code: 'qmojnT3SUhteRFn4',
      type: 'agent',
      id: 60,
    },
    {
      name: '技术匹配报告',
      icon: 'icon-startEvent',
      link: 'https://agent.rxyai.com/chat/RdzZo83XbCj3rome',
      code: 'RdzZo83XbCj3rome',
      type: 'agent',
      id: 61,
    },
    {
      name: '产品趋势研究',
      icon: 'icon-ic-chart',
      link: 'https://agent.rxyai.com/chat/oUcKep518lYzUcKe',
      code: 'oUcKep518lYzUcKe',
      type: 'agent',
      id: 62,
    },
    {
      name: '新品研发策略',
      icon: 'icon-daorudexinduoweibiaowenjiankuaijiefangshi',
      link: 'https://agent.rxyai.com/chat/uPnudphYfnOOXJqF',
      code: 'uPnudphYfnOOXJqF',
      type: 'agent',
      id: 63,
    },
    {
      name: '技术雷达扫描',
      icon: 'icon-leidatu',
      link: 'https://agent.rxyai.com/chat/qq88htTr9Qj6lz7p',
      code: 'qq88htTr9Qj6lz7p',
      type: 'agent',
      id: 60,
    },
  ],
  safe: [
    {
      name: '智谱AI',
      icon: 'icon-zhipuAI',
      link: 'https://agent.rxyai.com/chat/VebWgnoJ7viuKW4s',
      code: 'VebWgnoJ7viuKW4s',
      type: 'agent',
      id: 9,
    },
    {
      name: '通义千问',
      icon: 'icon-tongyiqianwenTongyi-Qianwen',
      link: 'https://agent.rxyai.com/chat/kflXarmzKGs2f3NP',
      code: 'kflXarmzKGs2f3NP',
      type: 'agent',
      id: 10,
    },
    {
      name: 'DeepSeek',
      icon: 'icon-deepseek',
      link: 'https://agent.rxyai.com/chat/AIIeM7NjVf73CthV',
      code: 'AIIeM7NjVf73CthV',
      type: 'agent',
      id: 11,
    },
  ],
  employee: [
    {
      name: '财务总监',
      icon: 'icon-Givecash',
      link: 'https://agent.rxyai.com/chat/UTr9ANvFWuJNx2mq',
      code: 'UTr9ANvFWuJNx2mq',
      type: 'agent',
      id: 12,
    },
    {
      name: '采购总监',
      icon: 'icon-caigou',
      link: 'https://agent.rxyai.com/chat/ki7BAQ6gMyr4wj9j',
      code: 'ki7BAQ6gMyr4wj9j',
      type: 'agent',
      id: 13,
    },
    {
      name: '营销总监',
      icon: 'icon-icon_',
      link: 'https://agent.rxyai.com/chat/JwM6YnLgKeDhl1Ng',
      code: 'JwM6YnLgKeDhl1Ng',
      type: 'agent',
      id: 14,
    },
    {
      name: '生产总监',
      icon: 'icon-shengchanzhizao',
      link: 'https://agent.rxyai.com/chat/hS4wKq4Tl3dqaQwY',
      code: 'hS4wKq4Tl3dqaQwY',
      type: 'agent',
      id: 15,
    },
    {
      name: '产品总监',
      icon: 'icon-chanpin',
      link: 'https://agent.rxyai.com/chat/kc1zp4Ig6JWl7Lx0',
      code: 'kc1zp4Ig6JWl7Lx0',
      type: 'agent',
      id: 16,
    },
    {
      name: '研发总监',
      icon: 'icon-yanfa11',
      link: 'https://agent.rxyai.com/chat/r4TXXSv1erfkz7X2',
      code: 'r4TXXSv1erfkz7X2',
      type: 'agent',
      id: 17,
    },
    {
      name: '人事总监',
      icon: 'icon-renshibiandongtongzhi',
      link: 'https://agent.rxyai.com/chat/QfHUXXMn8xa6Vv9X',
      code: 'QfHUXXMn8xa6Vv9X',
      type: 'agent',
      id: 18,
    },
    {
      name: '行政总监',
      icon: 'icon-crc',
      link: 'https://agent.rxyai.com/chat/YaSdCqNYJiQKyPQG',
      code: 'YaSdCqNYJiQKyPQG',
      type: 'agent',
      id: 19,
    },
    {
      name: '业绩增长助理',
      icon: 'icon-CARRER',
      link: 'https://agent.rxyai.com/chat/UHDAcJpFAP6VToIW',
      code: 'UHDAcJpFAP6VToIW',
      type: 'agent',
      id: 20,
    },
    {
      name: '私域销售',
      icon: 'icon-siyudian',
      link: 'https://agent.rxyai.com/chat/65xLwHCC64cn0fXq',
      code: '65xLwHCC64cn0fXq',
      type: 'agent',
      id: 21,
    },
    {
      name: 'CEO智能助理',
      icon: 'icon-CEO_MAX-',
      link: 'https://agent.rxyai.com/chat/OYNNAC2ZiwrGN0sx',
      code: 'OYNNAC2ZiwrGN0sx',
      type: 'agent',
      id: 22,
    },
    {
      name: '安全保密专员',
      icon: 'icon-zixunbaomi',
      link: 'https://agent.rxyai.com/chat/N6H9If5E1YDJiLY9',
      code: 'N6H9If5E1YDJiLY9',
      type: 'agent',
      id: 23,
    },
  ],
  information: [
    {
      name: '企业诊断报告',
      icon: 'icon-zhinengzhenduan',
      link: 'https://agent.rxyai.com/chat/xLQFuWbgYibdKkiZ',
      code: 'xLQFuWbgYibdKkiZ',
      type: 'agent',
      id: 24,
    },
    {
      name: '竞品情报分析',
      icon: 'icon-jingpinshuju',
      link: 'https://agent.rxyai.com/chat/4DZWiAUT3ngYsxuc',
      code: '4DZWiAUT3ngYsxuc',
      type: 'agent',
      id: 25,
    },
    {
      name: '客户洞察报告',
      icon: 'icon-kehudongcha',
      link: 'https://agent.rxyai.com/chat/eEogVfViaU4HVXC4',
      code: 'eEogVfViaU4HVXC4',
      type: 'agent',
      id: 26,
    },
    {
      name: '企业舆情监控',
      icon: 'icon-yuqingjiankong',
      link: 'https://agent.rxyai.com/chat/0sX1hrQVxYDY44jo',
      code: '0sX1hrQVxYDY44jo',
      type: 'agent',
      id: 27,
    },
    {
      name: '供应链风险识别',
      icon: 'icon-guanwang_kehuanli_qitakehu_yewutongdian_heguifengxianshibiebuzu',
      link: 'https://agent.rxyai.com/chat/dEQnHjfqNqUp6P1B',
      code: 'dEQnHjfqNqUp6P1B',
      type: 'agent',
      id: 28,
    },
  ],
  produce: [
    {
      name: '资产价值评估',
      icon: 'icon-zizhirenzheng',
      link: 'https://agent.rxyai.com/chat/bCujDOw5khWlX9r2',
      code: 'bCujDOw5khWlX9r2',
      type: 'agent',
      id: 29,
    },
    {
      name: '招商策划方案',
      icon: 'icon-zhaoshang',
      link: 'https://agent.rxyai.com/chat/LKeJjnGzmCZVQlPg',
      code: 'LKeJjnGzmCZVQlPg',
      type: 'agent',
      id: 30,
    },
    {
      name: '生产智能化',
      icon: 'icon-zhinengshengchan',
      link: 'https://agent.rxyai.com/chat/XLqwnxix0tYZbCgF',
      code: 'XLqwnxix0tYZbCgF',
      type: 'agent',
      id: 31,
    },
    {
      name: '目标客户建议',
      icon: 'icon-app_renliuliangtongji',
      link: 'https://agent.rxyai.com/chat/78qkHQZrCxBc2TpW',
      code: '78qkHQZrCxBc2TpW',
      type: 'agent',
      id: 32,
    },
    {
      name: '履约能力评估',
      icon: 'icon-daikuan',
      link: 'https://agent.rxyai.com/chat/1ThQXbmpNL1wI4n1',
      code: '1ThQXbmpNL1wI4n1',
      type: 'agent',
      id: 33,
    },
    {
      name: '项目进度管理',
      icon: 'icon-xiangmujindu1',
      link: 'https://agent.rxyai.com/chat/Lk0BD3jiv4bvYlrt',
      code: 'Lk0BD3jiv4bvYlrt',
      type: 'agent',
      id: 34,
    },
  ],
  market: [
    {
      name: '营销策略生成',
      icon: 'icon-yingxiaocelvefanganzhongxin',
      link: 'https://agent.rxyai.com/chat/ghouo053H3iDdOHs',
      code: 'ghouo053H3iDdOHs',
      type: 'agent',
      id: 35,
    },
    {
      name: '营销方案制定',
      icon: 'icon-yingxiaofangan',
      link: 'https://agent.rxyai.com/chat/bNWEostgD6LdYSzJ',
      code: 'bNWEostgD6LdYSzJ',
      type: 'agent',
      id: 36,
    },
    {
      name: '营销渠道管理',
      icon: 'icon-yingxiao-qudaohuomaicon',
      link: 'https://agent.rxyai.com/chat/mV4C7CNXD7mTW98f',
      code: 'mV4C7CNXD7mTW98f',
      type: 'agent',
      id: 37,
    },
    {
      name: '客群分析建议',
      icon: 'icon-newIcon',
      link: 'https://agent.rxyai.com/chat/rDFGvePxyIcwZl8P',
      code: 'rDFGvePxyIcwZl8P',
      type: 'agent',
      id: 38,
    },
    {
      name: '自媒体运营助手',
      icon: 'icon-sousuoyingxiao-10',
      link: 'https://agent.rxyai.com/chat/jReBEXMbDTHZdY11',
      code: 'jReBEXMbDTHZdY11',
      type: 'agent',
      id: 39,
    },
  ],
  ai: [
    {
      name: 'AI图像',
      icon: 'icon-icon_haibao',
      link: 'https://agent.rxyai.com/chat/pAhDx4OBN3K89wEk',
      code: 'pAhDx4OBN3K89wEk',
      type: 'agent',
      id: 40,
    },
    {
      name: 'AI视频',
      icon: 'icon-huaweishipin',
      link: 'https://agent.rxyai.com/chat/XML1S6hehoboTat7',
      code: 'XML1S6hehoboTat7',
      type: 'agent',
      id: 41,
    },
    {
      name: 'AI创意海报',
      icon: 'icon-yishulei',
      link: 'https://agent.rxyai.com/chat/kPiS2IbYVh2yg0hC',
      code: 'kPiS2IbYVh2yg0hC',
      type: 'agent',
      id: 42,
    },
  ],
  manage: [
    {
      name: '职位描述生成助手',
      icon: 'icon-a-bianzu11',
      link: 'https://agent.rxyai.com/completion/aJcL702PVcHdAIKp',
      code: 'aJcL702PVcHdAIKp',
      type: 'completion',
      id: 43,
    },
    {
      name: '简历筛选助手',
      icon: 'icon-renliziyuan',
      link: 'https://agent.rxyai.com/chat/A6aMQrBCBSv3KOWI',
      code: 'A6aMQrBCBSv3KOWI',
      type: 'agent',
      id: 44,
    },
    {
      name: '初选面试官',
      icon: 'icon-jianlishaixuan',
      link: 'https://agent.rxyai.com/chat/5LjVs9917O2ftIAR',
      code: '5LjVs9917O2ftIAR',
      type: 'agent',
      id: 45,
    },
    {
      name: '入职培训助手',
      icon: 'icon-dairuzhi',
      link: 'https://agent.rxyai.com/chat/Z6LxTdnn14rFTH23',
      code: 'Z6LxTdnn14rFTH23',
      type: 'agent',
      id: 46,
    },
    {
      name: '绩效考核助手',
      icon: 'icon-jixiaopingfen',
      link: 'https://agent.rxyai.com/chat/lYcFyWeZCIdWypue',
      code: 'lYcFyWeZCIdWypue',
      type: 'agent',
      id: 47,
    },
    {
      name: '员工心理健康考核',
      icon: 'icon-xinlijiankang',
      link: 'https://agent.rxyai.com/chat/lLRpEudcFFD4YRph',
      code: 'lLRpEudcFFD4YRph',
      type: 'agent',
      id: 48,
    },
    {
      name: '财务分析助手',
      icon: 'icon-caiwu',
      link: 'https://agent.rxyai.com/chat/9cgcZ4QamInJCrS0',
      code: '9cgcZ4QamInJCrS0',
      type: 'agent',
      id: 49,
    },
    {
      name: '合同审查助手',
      icon: 'icon-qianhetong',
      link: 'https://agent.rxyai.com/chat/VujggODnPvC40axz',
      code: 'VujggODnPvC40axz',
      type: 'agent',
      id: 50,
    },
    {
      name: '法律咨询助手',
      icon: 'icon--falvzixun',
      link: 'https://agent.rxyai.com/chat/6t8uDRTHFOISec7b',
      code: '6t8uDRTHFOISec7b',
      type: 'agent',
      id: 51,
    },
    {
      name: '法律案例检索',
      icon: 'icon-zhanneisousuo',
      link: 'https://agent.rxyai.com/chat/kNqXWPqAVk7i6UmM',
      code: 'kNqXWPqAVk7i6UmM',
      type: 'agent',
      id: 52,
    },
    {
      name: '法规检索检索',
      icon: 'icon-lianlujiankangjiancha',
      link: 'https://agent.rxyai.com/chat/Sj4DTo5Ox2XwM1lN',
      code: 'Sj4DTo5Ox2XwM1lN',
      type: 'agent',
      id: 53,
    },
  ],
  fitkc: [
    {
      name: '企业中心',
      img: new URL('@/assets/img/fdqf-logo.png', import.meta.url).href,
      link: 'https://ai.fitkc.cn/high-tech-search',
      code: 'high-tech-search',
      type: 'chain',
      id: 54,
    },
    {
      name: '科研云',
      img: new URL('@/assets/img/kyy-logo.png', import.meta.url).href,
      link: 'https://kyy.rndcloud.cn/',
      type: 'chain',
      id: 56,
    },
    {
      name: '产学研合作平台',
      img: new URL('@/assets/img/cgzh-logo.png', import.meta.url).href,
      link: 'https://ttc.fitkc.cn/',
      type: 'chain',
      id: 57,
    },
    {
      name: '人才培训',
      img: new URL('@/assets/img/qxb-logo.png', import.meta.url).href,
      link: 'https://www.new.qxbyun.com/',
      type: 'chain',
      id: 58,
    },
    {
      name: '科品电商',
      img: new URL('@/assets/img/kph-logo.png', import.meta.url).href,
      link: 'https://h5.hnkph.cn/static/html/pc.html#/',
      type: 'chain',
      id: 59,
    },
    {
      name: '科企高质量发展分析平台',
      img: new URL('@/assets/img/tqap-logo.png', import.meta.url).href,
      link: 'https://tqap.fitkc.cn/',
      type: 'chain',
      id: 55,
    },
  ],
})
const currentSub = ref([]) // 假设 currentSub 这样定义，如果实际不同需调整
const goActive = ref(9)
const hasSub = ref(true)
const toName = router.currentRoute.value.name

const subMenu = ref(appStore.subMenuData)
watch(
  () => appStore.subMenuData,
  (newData) => {
    // 当 appStore 内的 subMenuData 改变时，更新 subMenu 的值
    subMenu.value = newData || []
    currentSub.value = newData || []
    hasSub.value = currentSub.value.length > 0
    console.log('这是在监听里面 ===', route.query)
    // debugger
    if (route.query.origin) {
      const zIndex = currentSub?.value.findIndex(item =>
        item.path.includes(route.query.code),
      )
      if (zIndex > -1)
        menuItemClick(currentSub?.value[zIndex], 'isN')
    }
    else {
      currentSub.value.length > 0 && menuItemClick(currentSub?.value[0], 'isN')
    }

    // goActive.value = currentSub?.value[0].path
    // router.replace({
    //   path: router.currentRoute.value.path,
    //   query: JSON.parse(currentSub?.value[0].query),
    // })
    // router.push({
    //   path: currentSub?.value[0].path,
    //   query: JSON.parse(currentSub?.value[0].query),
    //   replace: true,
    // })
  },
)

watch(currentSub, (newValue, oldValue) => {
  // console.log("currentSub 从", oldValue, "变为", newValue);
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
watch(hasSub, (newValue, oldValue) => {
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
watch(goActive, (newValue, oldValue) => {
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
const menuItemClick = (item, type) => {
  goActive.value = item.path
  if (item.type === 'chain' || item.path.startsWith('http')) {
    window.open(item.path, '_blank')
    return
  }
  if (item.query) {
    router.push({
      name: item.component,
      query: JSON.parse(item.query),
    })
  }
  else {
    router.push({
      name: item.component,
    })
  }
}

onMounted(() => {
  const existingScript = document.querySelector('#iconfont-script')
  if (existingScript)
    return
  const script = document.createElement('script')
  script.id = 'iconfont-script'
  script.src = 'https://at.alicdn.com/t/c/font_4933970_349n7c3p0rs.js'
  document.body.appendChild(script)
  // 在组件卸载时取消监听
  return () => {
    // unregister()
  }
})
</script>

<template>
  <NLayoutSider
    v-if="hasSub"
    :collapsed="collapsed"
    content-style="padding: 18px;"
    collapse-mode="width"
    :collapsed-width="0"
    :width="240"
    show-trigger="arrow-circle"
  >
    <div v-for="(item, index) in currentSub" :key="index" class="relative">
      <div
        class="menu-item"
        :class="[goActive === item.path ? 'active' : '']"
        @click="menuItemClick(item)"
      >
        <div class="menu-item-title flex">
          <span class="menu-item-icon mr-1 pt-1">
            <img
              v-if="item.meta.icon.indexOf('images') > -1"
              :src="item.meta.icon"
              alt=""
              class="item-img"
            >
            <svg v-else class="icon" aria-hidden="true">
              <use :xlink:href="`#${item.meta.icon}`" />
            </svg>
          </span>
          <div class="menu-item-text">
            {{ item.meta.title }}
          </div>
        </div>
      </div>
    </div>
  </NLayoutSider>
</template>

<style scoped lang="less">
.n-layout-sider {
  background: #f9fbff;
  .icon {
    width: 26px;
    height: 26px;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .menu-item-title {
    flex-direction: row;
    justify-content: left;
    align-items: center;
  }
  .item-img {
    width: 26px;
  }
  .menu-item {
    background: #f6f7fc;
    border-radius: 5px;
    padding: 7px 10px;
    margin-bottom: 5px;
    position: relative;
    cursor: pointer;
    &.active {
      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: calc(50% - 10px);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-right: 8px solid #2355f4;
      }
    }
  }
}
</style>
