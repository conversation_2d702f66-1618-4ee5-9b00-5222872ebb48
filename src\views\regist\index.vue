<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { doRegist, getVerificationCode } from '@/api/user'
import defaultAvatar from '@/assets/img/logo2.png'
import { t } from '@/locales'
import { useBasicLayout } from '@/hooks/useBasicLayout'
const { isMobile } = useBasicLayout()

interface ModelType {
  password: string
  account: string
  passwordVerify: string
}

const router = useRouter()
const message = useMessage()
const user = ref<ModelType>(Object.create(null))

// 基础变量定义
function validateAccount(account: string) {
  if (!account)
    return false
  // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const emailRegex = /^1\d{10}$/
  return emailRegex.test(account)
}

const isCounting = ref(false)
const countdown = ref(60)
const buttonText = ref(t('reset.get_verification_code'))

// Slide verification variables
const showVerification = ref(false)
const isVerified = ref(false)
const sliderPosition = ref(0)
const sliderWidth = ref(0)
const isDragging = ref(false)
const sliderContainerRef = ref<HTMLDivElement | null>(null)
const isSubmitting = ref(false) // 新增：防止重复提交

// Define proceedWithRegistration first since it's referenced in verifySuccess
const proceedWithRegistration = async () => {
  // 防止重复提交
  if (isSubmitting.value)
    return

  try {
    isSubmitting.value = true
    const { account, passwordVerify } = user.value
    // Make sure to pass all three required parameters - username, password, and code (empty string for code)
    await doRegist(account, passwordVerify, '')
    message.success(t('reset.registration_success'))
    router.push('/Login')
  }
  catch (error) {
    message.error((error as Error).message)
    // 发生错误时，重置验证状态，允许用户重试
    isVerified.value = false
    showVerification.value = false
  }
  finally {
    isSubmitting.value = false
  }
}

// 添加防抖函数，避免多次触发验证
const debounce = (fn: Function, delay: number) => {
  let timer: any = null
  return function (this: any, ...args: any[]) {
    if (timer)
      clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 使用防抖处理滑块成功验证
const debouncedVerifySuccess = debounce(() => {
  // 如果已经验证通过或正在提交中，不再处理
  if (isVerified.value || isSubmitting.value)
    return

  sliderPosition.value = sliderWidth.value
  isVerified.value = true

  // 验证通过后延迟处理，避免多次触发
  setTimeout(() => {
    if (isVerified.value && !isSubmitting.value) {
      showVerification.value = false
      proceedWithRegistration()
    }
  }, 500)
}, 300)

// 更新verifySuccess调用防抖函数
const verifySuccess = () => {
  debouncedVerifySuccess()
}

// 优化onDrag函数，减少验证触发频率，增加提示点动态效果
const onDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging.value || !sliderContainerRef.value)
    return

  // Prevent default behavior like scrolling
  if (event.type === 'touchmove')
    event.preventDefault()

  const containerRect = sliderContainerRef.value.getBoundingClientRect()
  const containerWidth = containerRect.width
  sliderWidth.value = containerWidth - 44 // 44px for slider button width

  let clientX: number

  if ('touches' in event)
    clientX = event.touches[0].clientX
  else clientX = event.clientX

  let newPosition = clientX - containerRect.left - 22

  // Constrain slider position
  if (newPosition < 0)
    newPosition = 0
  if (newPosition > sliderWidth.value)
    newPosition = sliderWidth.value

  sliderPosition.value = newPosition

  // 根据滑块位置动态更新提示点样式
  updateSliderTips(newPosition)

  // 只有当滑块接近结束位置且未被验证时才触发验证
  if (newPosition >= sliderWidth.value * 0.95 && !isVerified.value && !isSubmitting.value)
    verifySuccess()
}

// 根据滑块位置更新提示点样式
const updateSliderTips = (position: number) => {
  if (!sliderContainerRef.value)
    return

  const tipElements = sliderContainerRef.value.querySelectorAll('.slider-tips span')
  if (!tipElements.length)
    return

  const containerWidth = sliderWidth.value + 44
  const section = containerWidth / (tipElements.length + 1)

  tipElements.forEach((tip, index) => {
    const tipPosition = section * (index + 1)
    const distance = Math.abs(position - tipPosition + 22)
    const scaleFactor = Math.max(1, 1.5 - distance / 100)
    const opacity = Math.max(0.2, 0.8 - distance / 150);

    (tip as HTMLElement).style.transform = `scale(${scaleFactor})`;
    (tip as HTMLElement).style.backgroundColor = `rgba(20, 184, 166, ${opacity})`
  })
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)

  // If not verified, reset the slider
  if (!isVerified.value)
    sliderPosition.value = 0
}

const startDrag = (event: MouseEvent | TouchEvent) => {
  isDragging.value = true
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('touchmove', onDrag, { passive: false })
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchend', stopDrag)

  // Prevent scrolling while dragging on mobile
  if (event.type === 'touchstart')
    event.preventDefault()
}

const handleValidateButtonClick = async (e: MouseEvent) => {
  e.preventDefault()

  // 如果正在提交中，阻止重复点击
  if (isSubmitting.value)
    return

  const { account, password, passwordVerify } = user.value
  if (!validateAccount(account)) {
    message.error(t('reset.invalid_account'))
    user.value.account = ''
    return
  }
  if (!passwordVerify) {
    message.error('请输入6-16位确认密码')
    return
  }
  if (password != passwordVerify) {
    message.error('两次密码不一致')
    return
  }

  // 显示验证前重置状态
  sliderPosition.value = 0
  isVerified.value = false
  isSubmitting.value = false

  // Show verification instead of proceeding directly
  showVerification.value = true
}

// 关闭验证模态框
const closeVerification = () => {
  // 如果正在提交中，不允许关闭
  if (isSubmitting.value)
    return

  showVerification.value = false
  sliderPosition.value = 0
  isVerified.value = false
}

async function startCountdown() {
  const checkResult = validateAccount(user.value.account)
  if (!checkResult)
    message.warning(t('reset.invalid_account'))

  if (isCounting.value)
    return

  try {
    await getVerificationCode(user.value.account)
  }
  catch (error) {
    message.warning((error as Error).message)
  }

  isCounting.value = true
  buttonText.value = countdown.value + t('reset.seconds_retry')

  const timer = setInterval(() => {
    countdown.value--
    buttonText.value = countdown.value + t('reset.seconds_retry')

    if (countdown.value === 0) {
      clearInterval(timer)
      isCounting.value = false
      countdown.value = 60
      buttonText.value = t('reset.get_verification_code')
    }
  }, 1000)
}
</script>

<template>
  <div id="app">
    <br>
    <div class="flex justify-center" data-v-0ee1b774="">
      <img
        style="width: 200px; height: 200px; border-radius: 60px; margin-top: -50px"
        :src="defaultAvatar"
        alt="FIT飞地科服AI智脑"
        class="w-fit hover:cursor-pointer md:mt-0 md:h-16"
        data-v-0ee1b774=""
      >
    </div>
    <div class="text-white text-center text-3xl font-bold" style="margin-top: -35px">
      FIT飞地科服AI智脑
    </div>
    <div
      class="relative w-full mt-4 overflow-hidden bg-white shadow-xl ring-1 ring-gray-900/5 sm:mx-auto sm:h-min sm:max-w-4xl sm:rounded-lg lg:max-w-5xl 2xl:max-w-6xl login-box"
      :style="{ width: isMobile ? '100%' : '880px' }"
      data-v-0ee1b774=""
    >
      <div class="px-6 pt-4 pb-8 sm:px-10 footer-login" data-v-0ee1b774="">
        <main class="mx-auto sm:max-w-4xl lg:max-w-5xl 2xl:max-w-6xl" data-v-0ee1b774="">
          <div
            class="nuxt-loading-indicator"
            style="
              position: fixed;
              top: 0px;
              right: 0px;
              left: 0px;
              pointer-events: none;
              width: 0%;
              height: 3px;
              opacity: 0;
              background: rgb(45, 212, 191);
              transition: width 0.1s ease 0s, height 0.4s ease 0s, opacity 0.4s ease 0s;
              z-index: 999999;
            "
          />
          <div>
            <div>
              <div
                class="flex flex-col justify-center min-h-full my-4 space-y-8 sm:px-6 lg:px-8"
              >
                <div class="sm:mx-auto sm:w-full sm:max-w-md">
                  <h2 class="text-3xl tracking-tight text-center text-gray-900">
                    {{ $t("register.create_account") }}
                  </h2>
                  <p class="mt-2 text-sm text-center text-gray-600">
                    {{ $t("reset.or") }}
                    <a
                      href="/login"
                      style="color: #0084ff"
                      class="font-semibold text-teal-500 hover:text-teal-600"
                    >{{ $t("reset.login") }}</a>
                    {{ $t("reset.if_account") }}
                  </p>
                </div>
                <div class="sm:mx-auto sm:w-full sm:max-w-sm">
                  <form
                    class="space-y-6"
                    :style="{
                      width: !isMobile ? '580px' : 'calc(100% - 20px)',
                      marginLeft: isMobile ? '10px' : 'calc(50% - 290px)',
                    }"
                  >
                    <div>
                      <label for="email" class="block text-sm font-medium text-gray-700">手机号</label>
                      <div class="mt-1">
                        <input
                          id="email"
                          v-model="user.account"
                          :allow-input="
                            (val) => {
                              return !/[^0-9]/g.test(val);
                            }
                          "
                          maxlength="11"
                          placeholder="请输入手机号"
                          name="email"
                          type="email"
                          autocomplete="email"
                          required="true"
                          class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:border-teal-500 focus:outline-none focus:ring-teal-500 sm:text-sm"
                        >
                      </div>
                    </div>
                    <!-- <div>
                      <label class="block text-sm font-medium text-gray-700">{{ $t('reset.verification_code') }}</label>
                      <div class="flex justify-between mt-1">
                        <input maxLength="6" v-model="user.verificationCode" :placeholder="$t('reset.enter_verification_code')" name="verification_code" type="text" required="true" class="w-2/3 px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:border-teal-500 focus:outline-none focus:ring-teal-500 sm:text-sm">
                        <button style="margin-top: 0;" type="button" @click="startCountdown" :disabled="isCounting" class="w-1/3 px-3 py-2 ml-2 text-sm font-medium text-white bg-teal-500 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">{{ buttonText }}</button>
                      </div>
                    </div> -->
                    <div>
                      <label
                        for="password"
                        class="block text-sm font-medium text-gray-700"
                      >{{ $t("reset.password") }}</label>
                      <div class="mt-1">
                        <input
                          id="password"
                          v-model="user.password"
                          maxLength="16"
                          :placeholder="$t('reset.enter_password')"
                          name="password"
                          type="password"
                          required="true"
                          class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:border-teal-500 focus:outline-none focus:ring-teal-500 sm:text-sm"
                        >
                      </div>
                    </div>
                    <div>
                      <label
                        for="passwordVerify"
                        class="block text-sm font-medium text-gray-700"
                      >确认密码</label>
                      <div class="mt-1">
                        <input
                          id="passwordVerify"
                          v-model="user.passwordVerify"
                          maxLength="16"
                          placeholder="请再次确认输入密码"
                          name="passwordVerify"
                          type="password"
                          required="true"
                          class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:border-teal-500 focus:outline-none focus:ring-teal-500 sm:text-sm"
                        >
                      </div>
                    </div>
                    <div>
                      <button
                        type="submit"
                        class="flex inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white transition bg-teal-500 bg-teal-600 border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:bg-teal-800 hover:bg-teal-700 focus:ring-teal-500 hover:bg-teal-600 focus:ring-teal-400"
                        @click="handleValidateButtonClick"
                      >
                        {{ $t("reset.register") }}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Slide verification popup -->
    <div v-if="showVerification" class="verification-overlay">
      <div class="verification-modal">
        <div class="verification-header">
          <h3>人机验证</h3>
          <button
            class="close-button"
            :disabled="isSubmitting"
            @click="closeVerification"
          >
            ×
          </button>
        </div>
        <div class="verification-content">
          <p>请将滑块拖动到最右边完成验证</p>
          <div
            ref="sliderContainerRef"
            class="slider-container"
            :class="{ verified: isVerified, submitting: isSubmitting }"
          >
            <div class="slider-track">
              <div v-if="!isVerified && !isSubmitting" class="slider-tips">
                <span /><span /><span /><span /><span />
              </div>
            </div>
            <div
              class="slider-button"
              :style="{ left: `${sliderPosition}px` }"
              :class="{ verified: isVerified, submitting: isSubmitting }"
              @mousedown="startDrag"
              @touchstart="startDrag"
            >
              <span v-if="!isVerified && !isSubmitting">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"
                  />
                </svg>
              </span>
              <span v-else-if="isVerified && !isSubmitting">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                  />
                </svg>
              </span>
              <span v-else class="loading-icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 16 16"
                >
                  <path
                    fill-rule="evenodd"
                    d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"
                  />
                  <path
                    d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"
                  />
                </svg>
              </span>
            </div>
            <div class="slider-progress" :style="{ width: `${sliderPosition}px` }" />
            <div v-if="!isVerified && !isSubmitting" class="slider-text">
              向右滑动验证
            </div>
            <div
              v-else-if="isVerified && !isSubmitting"
              class="slider-text verified-text"
            >
              验证通过
            </div>
            <div v-else class="slider-text submitting-text">
              处理中...
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <a target="_blank" style="color: #999999; font-size: 14px" href="https://fitkc.cn">
        Copyright © {{ new Date().getFullYear() }} 湖南飞地科技创业有限公司
      </a>
    </div>
  </div>
</template>

<style>
#app {
  background-image: url("@/assets/login-bg.jpeg");
  /* background-color: #141718; */
  background-size: 100% 100%;
  background-repeat: no-repeat;
  max-height: 100vh;
  /* overflow: hidden; */
}
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  margin-top: auto;
  position: absolute;
  bottom: 0;
}

/* Slide verification styles */
.verification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
  backdrop-filter: blur(2px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 增强滑块样式 */
.verification-modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #edf2f7;
  background-color: #f8fafc;
}

.verification-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2d3748;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 22px;
  cursor: pointer;
  color: #718096;
  transition: color 0.2s;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover:not(:disabled) {
  color: #e53e3e;
  background-color: rgba(0, 0, 0, 0.05);
}

.verification-content {
  padding: 24px;
}

.verification-content p {
  margin: 0 0 20px;
  color: #4a5568;
  text-align: center;
  font-size: 15px;
}

.slider-container {
  position: relative;
  height: 44px;
  background-color: #edf2f7;
  border-radius: 22px;
  margin: 20px 0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.slider-container:hover:not(.submitting) {
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.slider-track {
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: 22px;
  background: linear-gradient(145deg, #f0f0f0, #e6e6e6);
}

.slider-button {
  position: absolute;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(145deg, #14b8a6, #0f9488);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  z-index: 2;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 18px;
}

.slider-button:hover:not(.submitting) {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transform: scale(1.05);
}

.slider-button:active:not(.submitting) {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: scale(0.98);
}

.slider-progress {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, rgba(20, 184, 166, 0.1), rgba(20, 184, 166, 0.3));
  border-radius: 22px 0 0 22px;
  transition: width 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 0 10px rgba(20, 184, 166, 0.2);
}

.slider-container.verified {
  background-color: rgba(16, 185, 129, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.slider-container.verified .slider-progress {
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.4));
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.3);
}

.slider-container.verified .slider-button {
  background: linear-gradient(145deg, #10b981, #059669);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
  animation: success-pulse 1s;
}

.slider-text {
  position: absolute;
  width: 100%;
  text-align: center;
  line-height: 44px;
  color: #718096;
  font-size: 15px;
  font-weight: 500;
  user-select: none;
  pointer-events: none;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.verified-text {
  color: #059669;
  font-weight: 600;
}

.submitting-text {
  color: #d97706;
  animation: blink 1s infinite;
}

.slider-container.submitting .slider-button {
  background: linear-gradient(145deg, #f59e0b, #d97706);
  cursor: not-allowed;
  animation: rotate 1.5s infinite linear;
}

.close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes success-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes blink {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 添加滑动轨道提示点样式 */
.slider-tips {
  display: flex;
  justify-content: space-between;
  padding: 0 50px 0 50px;
  height: 100%;
  align-items: center;
}

.slider-tips span {
  display: block;
  width: 8px;
  height: 8px;
  background-color: rgba(74, 85, 104, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

/* 根据滑块位置改变提示点样式 */
.slider-container:not(.verified):not(.submitting) .slider-tips span:nth-child(1) {
  background-color: rgba(20, 184, 166, 0.4);
  transform: scale(1.2);
}

.slider-container:not(.verified):not(.submitting) .slider-tips span:nth-child(2) {
  background-color: rgba(20, 184, 166, 0.3);
  transform: scale(1.1);
}

.loading-icon {
  animation: rotate 1.5s infinite linear;
  display: inline-block;
}
</style>
