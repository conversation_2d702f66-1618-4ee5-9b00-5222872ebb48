<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { NAvatar, NButton, useDialog, useMessage } from 'naive-ui'
import html2canvas from 'html2canvas'
import to from 'await-to-js'
import drawListVue from '../mj/drawList.vue'
import aiGPT from '../mj/aiGpt.vue'
import AiSiderInput from '../mj/aiSiderInput.vue'
import aiGptInput from '../mj/aiGptInput.vue'
import { Message } from './components'
import { useScroll } from './hooks/useScroll'
import { useChat } from './hooks/useChat'
import { useUsingContext } from './hooks/useUsingContext'
import AvatarComponent from './components/Message/Avatar.vue'
import { getGpts } from '@/api/chatmsg'
import { SvgIcon } from '@/components/common'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { getToken } from '@/store/modules/auth/helper'
import {
  gptConfigStore,
  gptsUlistStore,
  homeStore,
  useAppStore,
  useChatStore,
  usePromptStore,
} from '@/store'
import type { gptsType } from '@/api'
import { chatSetting, fetchChatAPIProcess, mlog } from '@/api'
import { getChatHistory } from '@/api/chat'
import { t } from '@/locales'
import { getInform, readNotice } from '@/api/notice'

let controller = new AbortController()

const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === 'true'

const route = useRoute()
const dialog = useDialog()
const ms = useMessage()
const router = useRouter()
const chatStore = useChatStore()
// const href = window.location.href.split('#')[0]

const href = window.location.hostname
const { isMobile } = useBasicLayout()
const { addChat, updateChat, updateChatSome, getChatByUuidAndIndex } = useChat()
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll()
const { usingContext, toggleUsingContext } = useUsingContext()

const { uuid } = route.params as { uuid: string }
const uuid1 = chatStore.active
// eslint-disable-next-line new-cap
const chatSet = new chatSetting(uuid1 == null ? 1002 : uuid1)
const nGptStore = ref(chatSet.getGptConfig())
const dataSources = ref<Chat.Chat[]>([])
// const dataSources = computed(() => chatStore.getChatByUuid(+uuid))
const getGpt = async () => {
  const params = {
    pageNum: 1,
    pageSize: 99,
    modelName: nGptStore.value.model || 'deepseek/deepseek-r1/community',
    sessionId: uuid,
  }
  getChatHistory(params).then((res) => {
    dataSources.value
      = res.data.rows
      && res.data.rows.map((item: any) => {
        return {
          dateTime: item.createTime,
          text: item.content,
          inversion: item.role === 'user',
          error: false,
          loading: false,
          id: item.id,
          uuid: item.sessionId,
        }
      })
  })
}
const conversationList = computed(() =>
  dataSources.value.filter(item => !item.inversion && !!item.conversationOptions),
)

const prompt = ref<string>('')
const loading = ref<boolean>(false)
const inputRef = ref<Ref | null>(null)

// 添加PromptStore
const promptStore = usePromptStore()

// 使用storeToRefs，保证store修改后，联想部分能够重新渲染
const { promptList: promptTemplate } = storeToRefs<any>(promptStore)

const appStore = useAppStore()
const isChat = computed(() => appStore.isChat)
const typeingText = ref<string>('')
const buffer = ref<any>('')

// 未知原因刷新页面，loading 状态不会重置，手动重置
dataSources.value.forEach((item, index) => {
  if (item.loading)
    updateChatSome(+uuid, index, { loading: false })
})
const sendSubmit = async (val: any) => {
  console.log('sendSubmit 发送了消息', val)
  loading.value = true
  dataSources.value.push({
    dateTime: new Date().getTime(),
    text: prompt.value,
    inversion: true,
    error: false,
    loading: false,
    uuid,
  })
  scrollToBottom()
  const param = {
    frequency_penalty: nGptStore.value.frequency_penalty || 0,
    kid: nGptStore.value.kid || '',
    max_tokens: nGptStore.value.max_tokens || 4096,
    model: nGptStore.value.model || 'deepseek/deepseek-r1/community',
    presence_penalty: nGptStore.value.presence_penalty || 0,
    temperature: nGptStore.value.temperature || 0.7,
    top_p: nGptStore.value.top_p || 1,
    part: 'user',
    search: false,
    sessionId: uuid,
    messages: [
      {
        role: 'user',
        content: prompt.value,
      },
    ],
  }
  const response = await fetch(`${import.meta.env.VITE_GLOB_API_URL}/chat/send`, {
    mode: 'cors', // 显式设置请求模式为 CORS
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${getToken()}`,
    },
    body: JSON.stringify(param),
    signal: controller.signal,
  })
  await parseSSEResponse(response)
} // 结束原函数
async function parseSSEResponse(response: any) {
  // 创建读取器
  const reader = response.body.getReader()
  const decoder = new TextDecoder()
  // 持续读取流数据
  while (true) {
    const { done, value } = await reader.read()
    if (done) {
      // 流文件读取完毕
      loading.value = false
      if (typeingText.value.includes('<think>')) {
        const regex = /<think>([\s\S]*?)<\/think>/ // 正则表达式匹配第一个逗号和下一个逗号之间的内容
        const result = typeingText.value.match(regex)
        dataSources.value.push({
          think: result[0],
          dateTime: new Date().getTime(),
          text: extractTextAfterThinkTag(typeingText.value),
          inversion: false,
          error: false,
          loading: false,
          uuid,
        })
      }
      else if (typeingText.value) {
        dataSources.value.push({
          dateTime: new Date().getTime(),
          text: typeingText.value || '服务器繁忙，请稍后再试！',
          inversion: false,
          error: false,
          loading: false,
          uuid,
        })
      }
      scrollToBottom()
      typeingText.value = ''
      prompt.value = ''
      break
    }
    // 处理数据块
    const chunk = decoder.decode(value)
    processSSEChunk(chunk)
  }
}

const processSSEChunk = (chunk) => {
  // 沿用之前的缓冲区处理逻辑
  buffer.value += chunk

  while (true) {
    const endIndex = buffer.value.indexOf('\n\n')
    if (endIndex === -1)
      break

    const eventData = buffer.value.substring(0, endIndex)
    // eventData = eventData.replace(/^undefined/, '');
    buffer.value = buffer.value.substring(endIndex + 2)

    const parsed = parseEvent(eventData)
    // 更新到界面...
  }
}

// 解析 SSE 事件数据的方法
const parseEvent = (rawEventData) => {
  // 初始化事件对象
  const event = {
    id: null, // 事件ID（用于断点续传）
    type: 'message', // 事件类型（默认message）
    data: '', // 核心数据内容
    retry: null, // 重连时间（毫秒）
  }
  // 按行分割原始数据
  const lines = rawEventData.replace(/^undefined/, '').split('\n')
  // 逐行解析字段
  lines.forEach((line) => {
    // 跳过空行和注释行（以冒号开头的行）
    if (line.trim() === '' || line.startsWith(':'))
      return

    // 分解字段名和值（首个冒号作为分隔符）
    const colonIndex = line.indexOf(':')
    if (colonIndex === -1)
      return // 忽略无效行

    const field = line.slice(0, colonIndex).trim()
    const value = line.slice(colonIndex + 1).trimStart()
    // 处理特殊字段
    switch (field) {
      case 'id':
        event.id = value
        break

      case 'event':
        event.type = value
        break

      case 'retry':
        event.retry = parseInt(value, 10)
        break

      case 'data':
        // 支持多行data字段（自动拼接）
        event.data += `${value}\n`
        break

      default:
        // 忽略未知字段
        break
    }
  })

  // 清理data末尾的换行符
  if (event.data.endsWith('\n'))
    event.data = event.data.slice(0, -1)

  // 处理实际业务数据（假设data是JSON）
  try {
    const parsedData = JSON.parse(event.data)
    handleStreamData(parsedData)
  }
  catch (e) {
    // this.loading = false;
    console.error('SSE数据解析失败:', event.data)
    dataSources.value.push({
      dateTime: new Date().getTime(),
      text: event.data,
      inversion: false,
      error: false,
      loading: false,
      uuid,
    })
  }
  return event
}
const handleStreamData = (parsedData) => {
  // 实时更新到界面‘
  typeingText.value += parsedData.choices[0].delta.content
  console.log('this.typeingText', typeingText.value)

  // if (parsedData.type === 'delta') {
  //   // 增量更新模式（逐字显示）
  //   this.typeingText += parsedData.content;
  //   this.messages[this.messages.length - 1].content = this.currentAnswer;
  // } else if (parsedData.type === 'full') {
  //   // 全量替换模式
  //   this.messages[this.messages.length - 1].content = parsedData.content;
  // }

  // // 自动滚动到底部
  scrollToBottom()
}
function escapeHtml(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .replace(/\n/g, '<br>')
}
const extractTextAfterThinkTag = (inputStr) => {
  const tag = '</think>'
  const index = inputStr.indexOf(tag)

  if (index === -1)
    return '' // 如果不存在标签，返回空字符串
  return inputStr.slice(index + tag.length).trim() // 自动去除前后空格
}

async function onRegenerate(index: number) {
  if (loading.value)
    return

  controller = new AbortController()

  const { requestOptions } = dataSources.value[index]

  let message = requestOptions?.prompt ?? ''

  let options: Chat.ConversationRequest = {}

  if (requestOptions.options)
    options = { ...requestOptions.options }

  loading.value = true

  updateChat(+uuid, index, {
    dateTime: new Date().toLocaleString(),
    text: '',
    inversion: false,
    error: false,
    loading: true,
    conversationOptions: null,
    requestOptions: { prompt: message, options: { ...options } },
  })

  try {
    let lastText = ''
    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess<Chat.ConversationResponse>({
        prompt: message,
        options,
        signal: controller.signal,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target
          const { responseText } = xhr
          // Always process the final line
          const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2)
          let chunk = responseText
          if (lastIndex !== -1)
            chunk = responseText.substring(lastIndex)
          try {
            const data = JSON.parse(chunk)
            updateChat(+uuid, index, {
              dateTime: new Date().toLocaleString(),
              text: lastText + (data.text ?? ''),
              inversion: false,
              error: false,
              loading: true,
              conversationOptions: {
                conversationId: data.conversationId,
                parentMessageId: data.id,
              },
              requestOptions: { prompt: message, options: { ...options } },
            })

            if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
              options.parentMessageId = data.id
              lastText = data.text
              message = ''
              return fetchChatAPIOnce()
            }
          }
          catch (error) {
            //
          }
        },
      })
      updateChatSome(+uuid, index, { loading: false })
    }
    await fetchChatAPIOnce()
  }
  catch (error: any) {
    if (error.message === 'canceled') {
      updateChatSome(+uuid, index, {
        loading: false,
      })
      return
    }

    const errorMessage = error?.message ?? t('common.wrong')

    updateChat(+uuid, index, {
      dateTime: new Date().toLocaleString(),
      text: errorMessage,
      inversion: false,
      error: true,
      loading: false,
      conversationOptions: null,
      requestOptions: { prompt: message, options: { ...options } },
    })
  }
  finally {
    loading.value = false
  }
}

function handleExport() {
  if (loading.value)
    return

  const d = dialog.warning({
    title: t('chat.exportImage'),
    content: t('chat.exportImageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: async () => {
      try {
        d.loading = true
        const ele = document.getElementById('image-wrapper')
        const canvas = await html2canvas(ele as HTMLDivElement, {
          useCORS: true,
        })
        const imgUrl = canvas.toDataURL('image/png')
        const tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = imgUrl
        tempLink.setAttribute('download', 'chat-shot.png')
        if (typeof tempLink.download === 'undefined')
          tempLink.setAttribute('target', '_blank')

        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
        window.URL.revokeObjectURL(imgUrl)
        d.loading = false
        ms.success(t('chat.exportSuccess'))
        Promise.resolve()
      }
      catch (error: any) {
        ms.error(t('chat.exportFailed'))
      }
      finally {
        d.loading = false
      }
    },
  })
}

function handleDelete(index: number) {
  if (loading.value)
    return

  dialog.warning({
    title: t('chat.deleteMessage'),
    content: t('chat.deleteMessageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.deleteChatByUuid(+uuid, index)
    },
  })
}

function handleClear() {
  if (loading.value)
    return

  dialog.warning({
    title: t('chat.clearChat'),
    content: t('chat.clearChatConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.clearChatByUuid(+uuid)
    },
  })
}

function handleStop() {
  if (loading.value) {
    // homeStore.setMyData({ act: 'abort' })
    controller.abort()
    loading.value = false
  }
}

// 可优化部分
// 搜索选项计算，这里使用value作为索引项，所以当出现重复value时渲染异常(多项同时出现选中效果)
// 理想状态下其实应该是key作为索引项,但官方的renderOption会出现问题，所以就需要value反renderLabel实现
const searchOptions = computed(() => {
  if (prompt.value.startsWith('/')) {
    const abc = promptTemplate.value
      .filter((item: { key: string }) =>
        item.key.toLowerCase().includes(prompt.value.substring(1).toLowerCase()),
      )
      .map((obj: { value: any }) => {
        return {
          label: obj.value,
          value: obj.value,
        }
      })
    mlog('搜索选项', abc)
    return abc
  }
  else if (prompt.value == '@') {
    const abc = gptsUlistStore.myData.slice(0, 10).map((v: gptsType) => {
      return {
        label: v.info,
        gpts: v,
        value: v.gid,
      }
    })
    return abc
  }
  else {
    return []
  }
})

const goUseGpts = async (item: gptsType) => {
  console.log('goUseGpts', item)
  const saveObj = { model: `${item.modelName}`, gpts: item }
  gptConfigStore.setMyData(saveObj)
  if (chatStore.active) {
    // 保存到对话框
    const chatSet = new chatSetting(chatStore.active)
    if (chatSet.findIndex() > -1)
      chatSet.save(saveObj)
  }
  ms.success(t('mjchat.success2'))
  // const gptUrl= `https://gpts.ddaiai.com/open/gptsapi/use`;
  // myFetch(gptUrl,item );

  mlog('go local ', homeStore.myData.local)
  // router.replace({ name: 'Chat', params: { uuid: chatStore.active } })
  // if (homeStore.myData.local !== 'Chat')
  //   router.replace({ name: 'dev' })

  gptsUlistStore.setMyData(item)
}

// value反渲染key
const renderOption = (option: { label: string; gpts?: gptsType }) => {
  if (prompt.value == '@') {
    // return [ h( NAvatar,{src:'https://cos.aitutu.cc/gpts/gpt4all.jpg',size:"small",round:true}),option.label ]
    return [
      h(
        'div',
        {
          class: 'flex justify-start items-center',
          onclick: () => {
            if (option.gpts)
              goUseGpts(option.gpts)
            prompt.value = ''
            setTimeout(() => (prompt.value = ''), 80)
          },
        },
        [
          h(NAvatar, {
            'src': option.gpts?.logo,
            'fallback-src': 'https://cos.aitutu.cc/gpts/3.5net.png',
            'size': 'small',
            'round': true,
            'class': 'w-8 h-8',
          }),
          h('span', { class: 'pl-1' }, option.gpts?.name),
          h('span', { class: 'line-clamp-1 flex-1 pl-1 opacity-50' }, option.label),
        ],
      ),
    ]
  }
  for (const i of promptTemplate.value) {
    if (i.value === option.label)
      return [i.key]
  }

  return []
}

const placeholder = computed(() => {
  if (isMobile.value)
    return t('chat.placeholderMobile')
  return t('chat.placeholder')
})

const buttonDisabled = computed(() => {
  return loading.value || !prompt.value || prompt.value.trim() === ''
})

const footerClass = computed(() => {
  let classes = ['p-4']
  if (isMobile.value)
    classes = ['sticky', 'left-0', 'bottom-0', 'right-0', 'p-2', 'pr-3'] // , 'overflow-hidden'
  return classes
})

onMounted(() => {
  scrollToBottom()
  if (inputRef.value && !isMobile.value)
    inputRef.value?.focus()
  // 查询公告信息
  // 查询通知信息
  selectInform()
  getGpt()
  setTimeout(() => {
    scrollToBottom()
  }, 100)
})

onUnmounted(() => {
  if (loading.value)
    controller.abort()
  homeStore.setMyData({ isLoader: false })
})

const local = computed(() => homeStore.myData.local)
watch(
  () => homeStore.myData.act,
  (n) => {
    if (n == 'draw')
      scrollToBottom()
    if (n == 'scrollToBottom')
      scrollToBottom()
    if (n == 'scrollToBottomIfAtBottom')
      scrollToBottomIfAtBottom()
    if (n == 'gpt.submit' || n == 'gpt.resubmit')
      loading.value = true

    if (n == 'stopLoading')
      loading.value = false
  },
)
const st = ref({ inputme: true })

watch(
  () => loading.value,
  n => homeStore.setMyData({ isLoader: n }),
)

const ychat = computed(() => {
  let text = prompt.value
  if (loading.value)
    text = ''
  else scrollToBottomIfAtBottom()

  return { text, dateTime: t('chat.preview') } as Chat.Chat
})

const showModal = ref(false)
const modalContent = ref('<h2>暂无内容</h2>')
const informContent = ref([])
const noticeId = ref('')

async function selectInform() {
  const [err, result] = await to(getInform())
  if (result?.rows)
    informContent.value = result.rows.length ? result.rows : []
}

async function handleClose() {
  await to(readNotice(noticeId.value))
}
const gptsList = ref<gptsType[]>([])
const gptsFilterList = ref<gptsType[]>([])
const getRandowNum = (Min: number, Max: number): number => {
  const Range = Max - Min + 1
  const Rand = Math.random()
  return Min + Math.floor(Rand * Range)
}
const load = async () => {
  // const gptUrl= homeStore.myData.session.gptUrl?  homeStore.myData.session.gptUrl :'';
  // mlog('load',gptUrl );
  //  let d;
  // if( homeStore.myData.session.gptUrl ){
  //    d = await my2Fetch( homeStore.myData.session.gptUrl  );
  // }else {

  //     d = await myFetch('https://gpts.ddaiai.com/open/gpts');
  // }

  const params = { pageNum: 1, pageSize: 20 }
  const [err, result] = await to(getGpts(params))
  if (err)
    console.log('err===', err)
  else gptsList.value = (result.rows as unknown) as gptsType[]

  // gptsList.value = d.gpts as gptsType[];
  if (gptsList.value.length && gptsList.value.length > 3)
    gptsFilterList.value = gptsList.value.slice(0, 4)
}
const refresh = () => {
  gptsFilterList.value = []
  const num = gptsList.value[getRandowNum(0, gptsList.value.length - 1)]
  const num1 = gptsList.value[getRandowNum(0, gptsList.value.length - 1)]
  const num2 = gptsList.value[getRandowNum(0, gptsList.value.length - 1)]
  const num3 = gptsList.value[getRandowNum(0, gptsList.value.length - 1)]
  const arr = [num, num1, num2, num3]
  if (Array.from(new Set(arr)).length != 4) {
    refresh()
    return
  }
  gptsFilterList.value = [num, num1, num2, num3]
}
load()
</script>

<template>
  <div
    class="flex flex-col w-full h-full chat-content"
    :class="[isMobile ? '' : 'chat-content-noMobile']"
  >
    <!-- v-if="isMobile" -->
    <!-- <HeaderComponent
			:haveData="!!dataSources.length"
			:using-context="usingContext"
			@export="handleExport"
			@handle-clear="handleClear"
		/> -->

    <main class="flex-1 overflow-hidden">
      <div id="scrollRef" ref="scrollRef" class="h-full overflow-hidden overflow-y-auto">
        <!-- <div class="new-chat-header">对话框</div> -->
        <!-- max-w-screen-xl -->
        <div
          id="image-wrapper"
          class="w-full max-w-[1100px] m-auto dark:bg-[#101014]"
          :class="[isMobile ? 'p-2' : 'p-4']"
        >
          <template v-if="!dataSources.length">
            <div
              v-if="homeStore.myData.session.notify"
              class="text-neutral-300 mt-4"
              v-html="homeStore.myData.session.notify"
            />

            <div v-else class="gpts-box">
              <br>

              <br>
              <div v-if="local !== 'draw'">
                <!--							<h1>{{ href }}</h1> -->

                <div v-if="gptsFilterList && gptsFilterList.length" class="help">
                  <!-- <div class="ai-icon">
                    <IconSvg
                      icon="chatGPT"
                      :width="isMobile ? '32px' : '64px'"
                      :height="isMobile ? '32px' : '64px'"
                    />
                  </div> -->

                  <div
                    class="text text-center"
                    :style="{
                      'padding': isMobile ? '22px 10px' : '22px 27px 5px',
                      'font-size': isMobile ? '14px' : '16px',
                      'line-height': isMobile ? '20px' : '28px',
                    }"
                  >
                    <i
                      class="icon-zhinengkefu iconfont"
                      style="color: #409eff; font-size: 80px; margin: 0 auto"
                    />
                    <p class="title">
                      <br>
                      {{ t("chat.helpTitle") }}
                    </p>
                    <p
                      v-for="(item, index) in t('chat.helpcontent').split(';')"
                      :key="index"
                      class="subtitle"
                    >
                      {{ item }}
                    </p>
                    <!-- <div class="gpts-list">
                      <div class="refresh" @click="refresh">
                        <IconSvg icon="refresh" />&nbsp;{{ t("chat.refresh") }}
                      </div>
                      <div
                        v-for="v in gptsFilterList"
                        :key="v.name"
                        class="gpts-item"
                        :style="{
                          'width': isMobile ? '100%' : 'calc(50% - 20px)',
                          'marginRight': '20px',
                          'padding': isMobile ? '5px 8px' : '14px 10px',
                          'margin-bottom': isMobile ? '8px' : '20px',
                        }"
                      >
                        <NImage
                          :src="v.logo"
                          :preview-disabled="true"
                          lazy
                          class="group-hover:scale-[130%] duration-300 shrink-0 overflow-hidden bg-base object-cover rounded-full bc-avatar w-[80px] h-[80px]"
                          :style="{
                            width: isMobile ? '23px' : '46px',
                            height: isMobile ? '23px' : '46px',
                          }"
                        >
                          <template #placeholder>
                            <div class="w-full h-full justify-center items-center flex">
                              <SvgIcon
                                icon="line-md:downloading-loop"
                                class="text-[60px] text-green-300"
                              />
                            </div>
                          </template>
                        </NImage>
                        <div
                          :style="{
                            width: `calc(100% - ${isMobile ? '43px' : '66px'})`,
                            float: 'left',
                            marginLeft: '10px',
                          }"
                        >
                          <p class="info" :title="v.info">
                            {{ v.info }}
                          </p>
                          <p class="name" @click="goUseGpts(v)">
                            {{ t("chat.used") }} {{ v.name }}
                          </p>
                        </div>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="flex items-center justify-center mt-4 text-center text-neutral-300" v-else>
							<SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" />
							<span>Aha~</span>
						</div> -->
          </template>

          <template v-else>
            <div>
              <Message
                v-for="(item, index) of dataSources"
                :key="index"
                :date-time="item.dateTime"
                :text="item.text"
                :inversion="item.inversion"
                :error="item.error"
                :loading="item.loading"
                :chat="item"
                :index="index"
                @regenerate="onRegenerate(index)"
                @delete="handleDelete(index)"
              />
              <!-- <Message
                v-if="loading"
                :inversion="true"
                :date-time="$t('mj.typing')"
                :chat="ychat"
                :text="typeingText.value"
                :index="dataSources.length"
              /> -->
              <div v-if="loading" class="flex w-full mb-8 overflow-hidden">
                <div
                  class="flex items-center justify-center flex-shrink-0 h-8 overflow-hidden rounded-full basis-8 mr-2"
                >
                  <AvatarComponent :image="false" />
                </div>
                <div class="overflow-hidden text-sm items-start">
                  <div class="flex items-end gap-1 flex-row">
                    <div
                      class="text-black text-wrap min-w-[20px] max-w-[810px] rounded-md px-3 py-2 bg-[#f4f6f8] dark:bg-[#1e1e20] message-reply"
                    >
                      <div class="leading-relaxed break-words">
                        <div>
                          <!-- v-if --><!-- v-if --><!-- v-if -->
                          <div v-if="loading">
                            思考中...
                          </div>
                          <div
                            class="markdown-body"
                            :class="{ 'markdown-body-generate': loading }"
                            v-html="typeingText"
                          />
                          <!-- <div class="markdown-body">
                            <p>
                              您好！请问有什么可以帮到您的吗？如果您有任何关于科技企业服务的问题，或者需要了解相关政策、技术、市场动态等信息，请随时告诉我，我会尽力为您提供帮助。
                            </p>
                          </div> -->
                        </div>
                        <!-- <div v-else class="whitespace-pre-wrap" v-text="text" /> --><!-- v-if --><!-- v-if --><!-- v-if -->
                      </div>
                    </div>
                    <!-- <div class="flex flex-col" v-if="!chat.mjID && chat.model!='dall-e-3' && chat.model!='dall-e-2' "> -->
                    <div class="flex flex-col">
                      <!-- <button
            v-if="!inversion "
            class="mb-2 transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-300"
            @click="handleRegenerate"
          >
            <SvgIcon icon="ri:restart-line" />
          </button> --><!----><!---->
                    </div>
                  </div>
                </div>
              </div>
              <div class="sticky bottom-0 left-0 flex justify-center">
                <NButton v-if="loading" type="warning" @click="handleStop">
                  <template #icon>
                    <SvgIcon icon="ri:stop-circle-line" />
                  </template>
                  {{ t("common.stopResponding") }}
                </NButton>
              </div>
            </div>
          </template>
        </div>
      </div>
    </main>

    <footer v-if="local !== 'draw'" :class="footerClass" class="footer-content">
      <!-- max-w-screen-xl -->
      <div class="w-full max-w-[1100px] m-auto">
        <aiGptInput
          v-model:modelValue="prompt"
          :disabled="buttonDisabled"
          :search-options="searchOptions"
          :render-option="renderOption"
          @handle-clear="handleClear"
          @export="handleExport"
          @sendSub="sendSubmit"
        />
      </div>
    </footer>
  </div>

  <drawListVue />
  <aiGPT @finished="loading = false" />
  <AiSiderInput v-if="isMobile" :button-disabled="false" />
</template>

<style>
.new-chat-header {
  width: 100%;
  padding: 0 24px;
  height: 70px;
  line-height: 70px;
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 500;
}
</style>
