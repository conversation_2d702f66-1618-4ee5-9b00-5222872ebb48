<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();
const src = ref("");
const code = ref("");
const type = route.query.type;

onMounted(() => {
  code.value = decodeURIComponent(route.query.code);
});
</script>

<template>
  <div style="background-color: #efefef" class="chain-wrapper">
    <svg class="icon" aria-hidden="true">
      <use :xlink:href="`#${'icon-wailianliebiaoweikong'}`" />
    </svg>
    <span class="text-xl">点击左侧子菜单打开平台</span>
    <!-- <span v-else class="text-xl">已通过外链打开！</span> -->
  </div>
</template>

<style lang="less" scoped>
.chain-wrapper {
  height: calc(100vh - 56px);
  text-align: center;
  padding-top: 20%;
  .icon {
    width: 232px;
    height: 232px;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
    margin: 0 auto;
  }
}
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}
</style>
