<script setup lang="ts">
import { computed, ref } from 'vue'
import { NModal, NTabPane, NTabs } from 'naive-ui'
import General from './General.vue'
import Password from './Password.vue'
import { useAuthStore } from '@/store'
import { SvgIcon } from '@/components/common'

interface Props {
  visible: boolean
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
console.log('Setting 页面进来了么')
const authStore = useAuthStore()

const isChatGPTAPI = computed<boolean>(() => !!authStore.isChatGPTAPI)

const active = ref('General')

const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    header-style="padding: 10px 0 0 0"
    style="width: 100%; max-width: 680px; height: 540px"
    class="setting-modal"
  >
    <div>
      <NTabs v-model:value="active" type="line" animated>
        <NTabPane name="General" tab="General">
          <template #tab>
            <SvgIcon class="text-lg" icon="ri:file-user-line" />
            <span class="ml-2">{{ $t("setting.personal") }}</span>
          </template>
          <div class="min-h-[100px]">
            <General />
          </div>
        </NTabPane>

        <NTabPane name="SageSet" tab="safeset">
          <template #tab>
            <SvgIcon class="text-lg" icon="ri:list-settings-line" />
            <span class="ml-2">安全设置</span>
          </template>
          <div class="min-h-[100px]">
            <Password />
          </div>
        </NTabPane>

        <NTabPane v-if="isChatGPTAPI" name="Advanced" tab="Advanced">
          <template #tab>
            <SvgIcon class="text-lg" icon="ri:equalizer-line" />
            <!-- <span class="ml-2">{{ $t('setting.advanced') }}</span> -->
            <span class="ml-2">{{ $t("setting.model") }}</span>
          </template>
          <div class="min-h-[100px]">
            <!-- <Advanced /> -->
            <aiModel />
          </div>
        </NTabPane>

        <!-- <NTabPane name="chatmsg" tab="chatmsg">
          <template #tab>
            <SvgIcon class="text-lg" icon="mdi:message" />
            <span class="ml-2">{{ $t('setting.message') }}</span>
          </template>
          <aiMsg />
        </NTabPane> -->

        <!-- <NTabPane name="Config" tab="Config">
          <template #tab>
            <SvgIcon class="text-lg" icon="ri:list-settings-line" />
            <span class="ml-2">{{ $t("setting.about") }}</span>
          </template>
          <About />
        </NTabPane> -->
      </NTabs>
    </div>
  </NModal>
</template>

<style lang="less">
.setting-modal {
  .n-base-close {
    right: 30px;
    top: 15px;
    position: absolute;
    z-index: 99;
  }
}
</style>
